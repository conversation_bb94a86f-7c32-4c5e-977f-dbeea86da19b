(()=>{var e={};e.id=2689,e.ids=[2689],e.modules={3022:(e,s,t)=>{"use strict";t.d(s,{UsageTable:()=>r});let r=(0,t(6340).registerClientReference)(function(){throw Error("Attempted to call UsageTable() from the server but UsageTable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\profile\\usage\\components\\usage-table.tsx","UsageTable")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11518:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>C,metadata:()=>w});var r=t(94752),a=t(37838),i=t(1359),n=t(18815),d=t(24700),o=t(46954),l=t(70483),c=t(34069),u=t(62923),x=t(49499),m=t.n(x),h=t(44793),p=t(43131),b=t(24767);let g=(0,b.A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),f=(0,b.A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]),v=(0,b.A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]),j=(0,b.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);var y=t(36954),N=t(3022);let U="Cubent Units Usage",q="View your VS Code extension usage statistics and analytics.",w=(0,c.w)({title:U,description:q}),C=async()=>{let{userId:e}=await (0,a.j)(),s=await (0,i.N)();e&&s||(0,u.redirect)("/sign-in");let t=await n.database.user.findUnique({where:{clerkId:e},include:{usageMetrics:{orderBy:{date:"desc"},take:90},usageAnalytics:{orderBy:{createdAt:"desc"},take:100}}});t||(t=await n.database.user.create({data:{clerkId:e,email:s.emailAddresses[0]?.emailAddress||"",name:`${s.firstName||""} ${s.lastName||""}`.trim()||null,picture:s.imageUrl},include:{usageMetrics:{orderBy:{date:"desc"},take:90},usageAnalytics:{orderBy:{createdAt:"desc"},take:100}}}));let c=t.usageMetrics.reduce((e,s)=>({cubentUnitsUsed:e.cubentUnitsUsed+(s.cubentUnitsUsed||0),requestsMade:e.requestsMade+s.requestsMade}),{cubentUnitsUsed:0,requestsMade:0}),x=t.usageMetrics.slice(0,30),b=x.reduce((e,s)=>({cubentUnitsUsed:e.cubentUnitsUsed+(s.cubentUnitsUsed||0),requestsMade:e.requestsMade+s.requestsMade}),{cubentUnitsUsed:0,requestsMade:0});x.length>0&&(b.cubentUnitsUsed,x.length),x.length>0&&(b.requestsMade,x.length);let w=Object.entries(t.usageAnalytics.reduce((e,s)=>{let t=s.modelId||"unknown";return e[t]||(e[t]={cubentUnitsUsed:0,requestsMade:0}),e[t].cubentUnitsUsed+=s.cubentUnitsUsed||0,e[t].requestsMade+=s.requestsMade,e},{})).sort(([,e],[,s])=>s.cubentUnitsUsed-e.cubentUnitsUsed).slice(0,5),C=e=>e.toFixed(2);return(0,r.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(d.$,{variant:"ghost",size:"sm",asChild:!0,children:(0,r.jsxs)(m(),{href:"/profile",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Back to Profile"]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:U}),(0,r.jsx)("p",{className:"text-muted-foreground",children:q})]})]}),(0,r.jsxs)(d.$,{variant:"outline",size:"sm",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Refresh"]})]}),(0,r.jsxs)(o.Zp,{children:[(0,r.jsx)(o.aR,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(o.ZB,{className:"text-lg",children:"Usage Overview"}),(0,r.jsx)(l.E,{variant:"secondary",className:"bg-green-100 text-green-800 border-green-200",children:"User-based tracking (synced across devices)"})]})}),(0,r.jsx)(o.Wu,{children:(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-3xl font-bold",children:C(t.cubentUnitsUsed||0)}),(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:"Server Cubent Units"}),(0,r.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Limit: ",t.cubentUnitsLimit||50]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-3xl font-bold",children:c.requestsMade}),(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:"Server Messages"}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:"Total requests made"})]})]})})]}),(0,r.jsxs)(o.Zp,{children:[(0,r.jsx)(o.aR,{children:(0,r.jsx)(o.ZB,{className:"text-lg",children:"Top Models by Usage"})}),(0,r.jsxs)(o.Wu,{children:[w.length>0?(0,r.jsx)("div",{className:"space-y-3",children:w.map(([e,s])=>(0,r.jsxs)("div",{className:"flex items-center justify-between py-2",children:[(0,r.jsx)("div",{className:"font-medium text-sm",children:e}),(0,r.jsxs)("div",{className:"flex items-center gap-4 text-sm text-muted-foreground",children:[(0,r.jsxs)("span",{children:[C(s.cubentUnitsUsed)," units"]}),(0,r.jsxs)("span",{children:[s.requestsMade," msgs"]})]})]},e))}):(0,r.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,r.jsx)(g,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,r.jsx)("p",{children:"No usage data available"}),(0,r.jsx)("p",{className:"text-xs",children:"Start using the extension to see model breakdown"})]}),(0,r.jsxs)("div",{className:"mt-4 pt-3 border-t text-xs text-muted-foreground",children:["Last updated: ",new Date().toLocaleDateString("en-US",{month:"numeric",day:"numeric",year:"numeric"})]})]})]}),(0,r.jsxs)(o.Zp,{children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsx)(o.ZB,{className:"text-lg",children:"Usage Over Time"}),(0,r.jsx)(o.BT,{children:"Daily Cubent Units usage for the last 30 days"})]}),(0,r.jsx)(o.Wu,{children:(0,r.jsx)(y.UsageChart,{data:x.reverse()})})]}),(0,r.jsxs)(o.Zp,{children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsx)(o.ZB,{className:"text-lg",children:"Detailed Usage History"}),(0,r.jsx)(o.BT,{children:"Complete breakdown of your Cubent Units usage by day"})]}),(0,r.jsx)(o.Wu,{children:(0,r.jsx)(N.UsageTable,{data:t.usageMetrics})})]}),(0,r.jsxs)(o.Zp,{children:[(0,r.jsx)(o.aR,{children:(0,r.jsx)(o.ZB,{className:"text-lg",children:"Data Management"})}),(0,r.jsxs)(o.Wu,{children:[(0,r.jsxs)("div",{className:"flex flex-wrap gap-3",children:[(0,r.jsxs)(d.$,{variant:"outline",size:"sm",children:[(0,r.jsx)(f,{className:"h-4 w-4 mr-2"}),"Export Data"]}),(0,r.jsxs)(d.$,{variant:"outline",size:"sm",children:[(0,r.jsx)(v,{className:"h-4 w-4 mr-2"}),"Import Data"]}),(0,r.jsxs)(d.$,{variant:"outline",size:"sm",className:"text-red-600 hover:text-red-700",children:[(0,r.jsx)(j,{className:"h-4 w-4 mr-2"}),"Clear Data"]})]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mt-4",children:"Usage data is stored locally in your browser. Export your data to back it up or transfer it to another device."})]})]})]})}},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},13440:e=>{"use strict";e.exports=require("util/types")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},22427:(e,s,t)=>{"use strict";t.d(s,{UsageTable:()=>u});var r=t(99730),a=t(57752),i=t(74938),n=t(99752),d=t(72298),o=t(19161);let l=(0,o.A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]),c=(0,o.A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);function u({data:e}){let[s,t]=(0,a.useState)(1),[o,u]=(0,a.useState)(""),x=e=>e.toFixed(2),m=e=>e.toLocaleString(),h=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),p=e.filter(e=>h(e.date).toLowerCase().includes(o.toLowerCase())),b=Math.ceil(p.length/10),g=(s-1)*10,f=p.slice(g,g+10),v=e=>{t(Math.max(1,Math.min(e,b)))};return 0===e.length?(0,r.jsx)("div",{className:"text-center py-8 text-muted-foreground",children:(0,r.jsx)("p",{children:"No usage data available"})}):(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsxs)("div",{className:"relative flex-1 max-w-sm",children:[(0,r.jsx)(d.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,r.jsx)(n.p,{placeholder:"Search by date...",value:o,onChange:e=>{u(e.target.value),t(1)},className:"pl-10"})]})}),(0,r.jsx)("div",{className:"border rounded-lg overflow-hidden",children:(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{className:"bg-muted/50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"text-left p-4 font-medium",children:"Date"}),(0,r.jsx)("th",{className:"text-right p-4 font-medium",children:"Cubent Units"}),(0,r.jsx)("th",{className:"text-right p-4 font-medium",children:"Messages"}),(0,r.jsx)("th",{className:"text-right p-4 font-medium",children:"Avg Units/Message"})]})}),(0,r.jsx)("tbody",{children:f.map((e,s)=>(0,r.jsxs)("tr",{className:s%2==0?"bg-background":"bg-muted/25",children:[(0,r.jsx)("td",{className:"p-4",children:h(e.date)}),(0,r.jsx)("td",{className:"p-4 text-right font-mono",children:x(e.cubentUnitsUsed||0)}),(0,r.jsx)("td",{className:"p-4 text-right font-mono",children:m(e.requestsMade)}),(0,r.jsx)("td",{className:"p-4 text-right font-mono",children:e.requestsMade>0&&(e.cubentUnitsUsed||0)>0?x((e.cubentUnitsUsed||0)/e.requestsMade):"-"})]},e.id))})]})})}),b>1&&(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Showing ",g+1," to ",Math.min(g+10,p.length)," of ",p.length," entries"]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>v(s-1),disabled:1===s,children:[(0,r.jsx)(l,{className:"h-4 w-4"}),"Previous"]}),(0,r.jsx)("div",{className:"flex items-center space-x-1",children:Array.from({length:Math.min(5,b)},(e,t)=>{let a;return a=b<=5||s<=3?t+1:s>=b-2?b-4+t:s-2+t,(0,r.jsx)(i.$,{variant:s===a?"default":"outline",size:"sm",onClick:()=>v(a),className:"w-8 h-8 p-0",children:a},a)})}),(0,r.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>v(s+1),disabled:s===b,children:["Next",(0,r.jsx)(c,{className:"h-4 w-4"})]})]})]})]})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},36954:(e,s,t)=>{"use strict";t.d(s,{UsageChart:()=>r});let r=(0,t(6340).registerClientReference)(function(){throw Error("Attempted to call UsageChart() from the server but UsageChart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\profile\\usage\\components\\usage-chart.tsx","UsageChart")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},47803:(e,s,t)=>{Promise.resolve().then(t.bind(t,71932)),Promise.resolve().then(t.bind(t,22427)),Promise.resolve().then(t.bind(t,86332)),Promise.resolve().then(t.t.bind(t,41265,23)),Promise.resolve().then(t.bind(t,22683))},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},54287:e=>{"use strict";e.exports=require("console")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71932:(e,s,t)=>{"use strict";t.d(s,{UsageChart:()=>i});var r=t(99730),a=t(57752);function i({data:e}){let s=(0,a.useMemo)(()=>e.map(e=>({date:new Date(e.date).toLocaleDateString("en-US",{month:"short",day:"numeric"}),cubentUnits:e.cubentUnitsUsed||0,requests:e.requestsMade})),[e]),t=Math.max(...s.map(e=>e.cubentUnits)),i=Math.max(...s.map(e=>e.requests));return 0===s.length?(0,r.jsx)("div",{className:"h-64 flex items-center justify-center text-muted-foreground",children:(0,r.jsx)("p",{children:"No usage data available"})}):(0,r.jsxs)("div",{className:"h-64 w-full",children:[(0,r.jsx)("div",{className:"flex items-end justify-between h-full space-x-1",children:s.map((e,s)=>(0,r.jsxs)("div",{className:"flex flex-col items-center flex-1 h-full",children:[(0,r.jsxs)("div",{className:"flex-1 flex flex-col justify-end w-full space-y-1",children:[(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)("div",{className:"bg-blue-500 rounded-t-sm transition-all hover:bg-blue-600",style:{height:`${t>0?e.cubentUnits/t*100:0}%`,minHeight:e.cubentUnits>0?"2px":"0px"}}),(0,r.jsxs)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap",children:[e.cubentUnits.toFixed(2)," units"]})]}),(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)("div",{className:"bg-green-500 rounded-t-sm transition-all hover:bg-green-600",style:{height:`${i>0?e.requests/i*80:0}%`,minHeight:e.requests>0?"2px":"0px"}}),(0,r.jsxs)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap",children:[e.requests," messages"]})]})]}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground mt-2 transform -rotate-45 origin-left",children:e.date})]},s))}),(0,r.jsxs)("div",{className:"flex justify-center space-x-6 mt-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded"}),(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:"Cubent Units"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded"}),(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:"Messages"})]})]})]})}},73024:e=>{"use strict";e.exports=require("node:fs")},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},80481:e=>{"use strict";e.exports=require("node:readline")},81630:e=>{"use strict";e.exports=require("http")},82147:(e,s,t)=>{Promise.resolve().then(t.bind(t,36954)),Promise.resolve().then(t.bind(t,3022)),Promise.resolve().then(t.t.bind(t,21034,23)),Promise.resolve().then(t.t.bind(t,49499,23)),Promise.resolve().then(t.bind(t,93665))},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},91645:e=>{"use strict";e.exports=require("net")},94175:e=>{"use strict";e.exports=require("stream/web")},94735:e=>{"use strict";e.exports=require("events")},95617:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.default,__next_app__:()=>c,pages:()=>l,routeModule:()=>u,tree:()=>o});var r=t(57864),a=t(94327),i=t(70814),n=t(17984),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let o={children:["",{children:["(authenticated)",{children:["profile",{children:["usage",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,11518)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\profile\\usage\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,16703)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,52945,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,56940))).default(e)],apple:[async e=>(await Promise.resolve().then(t.bind(t,54526))).default(e)],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,36334))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,29622)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,70814)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,52945,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,56940))).default(e)],apple:[async e=>(await Promise.resolve().then(t.bind(t,54526))).default(e)],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,36334))).default(e)],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\profile\\usage\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(authenticated)/profile/usage/page",pathname:"/profile/usage",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[5319,7911,6239,903,7838,5480,3319,277,2644,7914,3051,4841,8004,1121,864,3781,6648,5432],()=>t(95617));module.exports=r})();
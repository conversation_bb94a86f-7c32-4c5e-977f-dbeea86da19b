(()=>{var e={};e.id=2689,e.ids=[2689],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},8086:e=>{"use strict";e.exports=require("module")},10783:(e,t,s)=>{"use strict";s.d(t,{Avatar:()=>N,AvatarFallback:()=>U,AvatarImage:()=>q});var r=s(99730),a=s(57752),i=s(1493),n=s(18526),o=s(62676),l=s(56750),d=s(52578);function u(){return()=>{}}var c="Avatar",[p,m]=(0,i.A)(c),[x,h]=p(c),f=a.forwardRef((e,t)=>{let{__scopeAvatar:s,...i}=e,[n,o]=a.useState("idle");return(0,r.jsx)(x,{scope:s,imageLoadingStatus:n,onImageLoadingStatusChange:o,children:(0,r.jsx)(l.sG.span,{...i,ref:t})})});f.displayName=c;var v="AvatarImage",g=a.forwardRef((e,t)=>{let{__scopeAvatar:s,src:i,onLoadingStatusChange:c=()=>{},...p}=e,m=h(v,s),x=function(e,{referrerPolicy:t,crossOrigin:s}){let r=(0,d.useSyncExternalStore)(u,()=>!0,()=>!1),i=a.useRef(null),n=r?(i.current||(i.current=new window.Image),i.current):null,[l,c]=a.useState(()=>j(n,e));return(0,o.N)(()=>{c(j(n,e))},[n,e]),(0,o.N)(()=>{let e=e=>()=>{c(e)};if(!n)return;let r=e("loaded"),a=e("error");return n.addEventListener("load",r),n.addEventListener("error",a),t&&(n.referrerPolicy=t),"string"==typeof s&&(n.crossOrigin=s),()=>{n.removeEventListener("load",r),n.removeEventListener("error",a)}},[n,s,t]),l}(i,p),f=(0,n.c)(e=>{c(e),m.onImageLoadingStatusChange(e)});return(0,o.N)(()=>{"idle"!==x&&f(x)},[x,f]),"loaded"===x?(0,r.jsx)(l.sG.img,{...p,ref:t,src:i}):null});g.displayName=v;var b="AvatarFallback",w=a.forwardRef((e,t)=>{let{__scopeAvatar:s,delayMs:i,...n}=e,o=h(b,s),[d,u]=a.useState(void 0===i);return a.useEffect(()=>{if(void 0!==i){let e=window.setTimeout(()=>u(!0),i);return()=>window.clearTimeout(e)}},[i]),d&&"loaded"!==o.imageLoadingStatus?(0,r.jsx)(l.sG.span,{...n,ref:t}):null});function j(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}w.displayName=b;var y=s(83590);function N({className:e,...t}){return(0,r.jsx)(f,{"data-slot":"avatar",className:(0,y.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function q({className:e,...t}){return(0,r.jsx)(g,{"data-slot":"avatar-image",className:(0,y.cn)("aspect-square size-full",e),...t})}function U({className:e,...t}){return(0,r.jsx)(w,{"data-slot":"avatar-fallback",className:(0,y.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},13440:e=>{"use strict";e.exports=require("util/types")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27559:(e,t,s)=>{"use strict";s.d(t,{UsageAnalytics:()=>T});var r=s(99730),a=s(57752),i=s(74938),n=s(58940),o=s(87785),l=s(1493),d=s(56750),u="Progress",[c,p]=(0,l.A)(u),[m,x]=c(u),h=a.forwardRef((e,t)=>{var s,a;let{__scopeProgress:i,value:n=null,max:o,getValueLabel:l=g,...u}=e;(o||0===o)&&!j(o)&&console.error((s=`${o}`,`Invalid prop \`max\` of value \`${s}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let c=j(o)?o:100;null===n||y(n,c)||console.error((a=`${n}`,`Invalid prop \`value\` of value \`${a}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let p=y(n,c)?n:null,x=w(p)?l(p,c):void 0;return(0,r.jsx)(m,{scope:i,value:p,max:c,children:(0,r.jsx)(d.sG.div,{"aria-valuemax":c,"aria-valuemin":0,"aria-valuenow":w(p)?p:void 0,"aria-valuetext":x,role:"progressbar","data-state":b(p,c),"data-value":p??void 0,"data-max":c,...u,ref:t})})});h.displayName=u;var f="ProgressIndicator",v=a.forwardRef((e,t)=>{let{__scopeProgress:s,...a}=e,i=x(f,s);return(0,r.jsx)(d.sG.div,{"data-state":b(i.value,i.max),"data-value":i.value??void 0,"data-max":i.max,...a,ref:t})});function g(e,t){return`${Math.round(e/t*100)}%`}function b(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function w(e){return"number"==typeof e}function j(e){return w(e)&&!isNaN(e)&&e>0}function y(e,t){return w(e)&&!isNaN(e)&&e<=t&&e>=0}v.displayName=f;var N=s(83590);function q({className:e,value:t,...s}){return(0,r.jsx)(h,{"data-slot":"progress",className:(0,N.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",e),...s,children:(0,r.jsx)(v,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})})}var U=s(10783),k=s(19161);let C=(0,k.A)("crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]]),A=(0,k.A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]),D=(0,k.A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),M=(0,k.A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var L=s(71265);let S=(0,k.A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]),E=(0,k.A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]);var P=s(41265),_=s.n(P);function R({data:e}){let t=(0,a.useMemo)(()=>{let t=new Date,s=new Date(t);s.setDate(t.getDate()-29);let r=[];for(let t=0;t<30;t++){let a=new Date(s);a.setDate(s.getDate()+t);let i=e.find(e=>new Date(e.date).toDateString()===a.toDateString());r.push({date:a.toLocaleDateString("en-US",{month:"short",day:"numeric"}),cubentUnits:i?.cubentUnitsUsed||0,requests:i?.requestsMade||0})}return r},[e]),s=Math.max(...t.map(e=>e.cubentUnits),1),i=Math.max(...t.map(e=>e.requests),1);return(0,r.jsxs)("div",{className:"h-80 w-full",children:[(0,r.jsx)("div",{className:"flex items-end justify-between h-full space-x-1 px-4",children:t.map((e,t)=>(0,r.jsxs)("div",{className:"flex flex-col items-center flex-1 h-full max-w-8",children:[(0,r.jsxs)("div",{className:"flex-1 flex flex-col justify-end w-full space-y-1",children:[(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)("div",{className:"bg-gradient-to-t from-blue-500 to-blue-400 rounded-t-sm transition-all hover:from-blue-600 hover:to-blue-500 shadow-sm",style:{height:`${s>0?Math.max(e.cubentUnits/s*180,3*(e.cubentUnits>0)):0}px`,width:"100%"}}),(0,r.jsxs)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10 shadow-lg",children:[(0,r.jsxs)("div",{className:"font-medium",children:[e.cubentUnits.toFixed(2)," units"]}),(0,r.jsxs)("div",{className:"text-gray-300",children:[e.requests," requests"]}),(0,r.jsx)("div",{className:"text-gray-300",children:e.date})]})]}),(0,r.jsx)("div",{className:"relative group",children:(0,r.jsx)("div",{className:"bg-gradient-to-t from-green-500 to-green-400 rounded-t-sm transition-all hover:from-green-600 hover:to-green-500 shadow-sm",style:{height:`${i>0?Math.max(e.requests/i*60,2*(e.requests>0)):0}px`,width:"100%"}})})]}),t%5==0&&(0,r.jsx)("div",{className:"text-xs text-muted-foreground mt-3 transform -rotate-45 origin-left whitespace-nowrap",children:e.date})]},t))}),(0,r.jsxs)("div",{className:"flex justify-center space-x-8 mt-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-4 h-4 bg-gradient-to-t from-blue-500 to-blue-400 rounded shadow-sm"}),(0,r.jsx)("span",{className:"text-sm font-medium text-muted-foreground",children:"Cubent Units"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-4 h-4 bg-gradient-to-t from-green-500 to-green-400 rounded shadow-sm"}),(0,r.jsx)("span",{className:"text-sm font-medium text-muted-foreground",children:"Messages"})]})]}),(0,r.jsx)("div",{className:"mt-4 text-center",children:(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Total: ",t.reduce((e,t)=>e+t.cubentUnits,0).toFixed(2)," units, "," ",t.reduce((e,t)=>e+t.requests,0)," messages"]})})]})}function T({initialData:e}){let[t,s]=(0,a.useState)(e),[l,d]=(0,a.useState)(!1),[u,c]=(0,a.useState)(new Date),p=async()=>{d(!0);try{let e=await fetch("/api/extension/usage/stats",{method:"GET",headers:{"Content-Type":"application/json"}});if(e.ok){let t=await e.json();t.success&&(s(e=>({...e,totalCubentUnits:t.totalCubentUnits||0,totalMessages:t.totalMessages||0,userLimit:t.userLimit||50,subscriptionTier:t.subscriptionTier||"free_trial"})),c(new Date))}}catch(e){console.error("Failed to refresh usage data:",e)}finally{d(!1)}},m=t.totalCubentUnits/t.userLimit*100,x=m>80,h=m>100,f=(e=>{switch(e){case"pro":return{name:"Pro",icon:C,color:"text-yellow-600"};case"premium":return{name:"Premium",icon:A,color:"text-purple-600"};default:return{name:"Free Trial",icon:D,color:"text-blue-600"}}})(t.subscriptionTier),v=f.icon;return(0,r.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(i.$,{variant:"ghost",size:"sm",asChild:!0,children:(0,r.jsxs)(_(),{href:"/profile",children:[(0,r.jsx)(M,{className:"h-4 w-4 mr-2"}),"Back to Profile"]})}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"Cubent Units Usage"}),t.isDemo&&(0,r.jsx)(o.E,{variant:"outline",className:"text-xs",children:"Demo Data"})]}),(0,r.jsx)("p",{className:"text-muted-foreground",children:t.isDemo?"Sample data shown - start using the extension to see real usage statistics.":"View your VS Code extension usage statistics and analytics."})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Last updated"}),(0,r.jsx)("p",{className:"text-sm font-medium",children:u.toLocaleTimeString()})]}),(0,r.jsxs)(i.$,{variant:"outline",size:"sm",onClick:p,disabled:l,children:[(0,r.jsx)(L.A,{className:`h-4 w-4 mr-2 ${l?"animate-spin":""}`}),"Refresh"]})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsx)(n.ZB,{children:"Account Information"}),(0,r.jsx)(n.BT,{children:"Your profile and subscription details"})]}),(0,r.jsxs)(n.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(U.Avatar,{className:"h-16 w-16",children:[(0,r.jsx)(U.AvatarImage,{src:t.user.picture,alt:t.user.name}),(0,r.jsx)(U.AvatarFallback,{children:t.user.name.charAt(0).toUpperCase()})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold",children:t.user.name}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:t.user.email})]})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-sm",children:"Subscription:"}),(0,r.jsxs)(o.E,{variant:"secondary",className:"flex items-center gap-2",children:[(0,r.jsx)(v,{className:`h-4 w-4 ${f.color}`}),f.name]})]})]})]}),(0,r.jsxs)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:[(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"Cubent Units"}),(0,r.jsx)(D,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:t.totalCubentUnits.toFixed(2)}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:[t.userLimit-t.totalCubentUnits," remaining of ",t.userLimit]}),(0,r.jsx)(q,{value:Math.min(m,100),className:"mt-3"}),(0,r.jsxs)("div",{className:"flex justify-between text-xs text-muted-foreground mt-2",children:[(0,r.jsxs)("span",{children:[m.toFixed(1),"% used"]}),(0,r.jsx)("span",{className:h?"text-red-600":x?"text-yellow-600":"text-green-600",children:h?"Over limit":x?"Near limit":"Within limit"})]})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"Total Messages"}),(0,r.jsx)(S,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:t.totalMessages.toLocaleString()}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Total requests made"})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"Efficiency"}),(0,r.jsx)(E,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:t.totalMessages>0?(t.totalCubentUnits/t.totalMessages).toFixed(2):"0.00"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Units per message"})]})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsx)(n.ZB,{children:"Usage Over Time"}),(0,r.jsx)(n.BT,{children:"Daily Cubent Units usage for the last 30 days"})]}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)(R,{data:t.chartData})})]}),(x||h)&&"free_trial"===t.subscriptionTier&&(0,r.jsx)(n.Zp,{className:"border-yellow-200 dark:border-yellow-800 bg-yellow-50 dark:bg-yellow-950/20",children:(0,r.jsx)(n.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(C,{className:"h-5 w-5 text-yellow-600 dark:text-yellow-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-yellow-800 dark:text-yellow-200",children:h?"Usage Limit Exceeded":"Approaching Usage Limit"}),(0,r.jsx)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300",children:"Upgrade to Pro for unlimited Cubent Units and advanced features."})]})]}),(0,r.jsx)(i.$,{className:"bg-yellow-600 hover:bg-yellow-700 text-white",children:"Upgrade Now"})]})})})]})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},31770:(e,t,s)=>{"use strict";s.d(t,{UsageAnalytics:()=>r});let r=(0,s(6340).registerClientReference)(function(){throw Error("Attempted to call UsageAnalytics() from the server but UsageAnalytics is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\profile\\usage\\components\\usage-analytics.tsx","UsageAnalytics")},33873:e=>{"use strict";e.exports=require("path")},34069:(e,t,s)=>{"use strict";s.d(t,{w:()=>l});var r=s(81121),a=s.n(r);let i="next-forge",n={name:"Vercel",url:"https://vercel.com/"},o=process.env.VERCEL_PROJECT_PRODUCTION_URL,l=({title:e,description:t,image:s,...r})=>{let l=`${e} | ${i}`,d={title:l,description:t,applicationName:i,metadataBase:o?new URL(`https://${o}`):void 0,authors:[n],creator:n.name,formatDetection:{telephone:!1},appleWebApp:{capable:!0,statusBarStyle:"default",title:l},openGraph:{title:l,description:t,type:"website",siteName:i,locale:"en_US"},publisher:"Vercel",twitter:{card:"summary_large_image",creator:"@vercel"}},u=a()(d,r);return s&&u.openGraph&&(u.openGraph.images=[{url:s,width:1200,height:630,alt:e}]),u}},34631:e=>{"use strict";e.exports=require("tls")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},40600:(e,t,s)=>{Promise.resolve().then(s.bind(s,31770))},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},52578:(e,t,s)=>{"use strict";e.exports=s(64959)},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},54287:e=>{"use strict";e.exports=require("console")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57416:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c,metadata:()=>u});var r=s(94752),a=s(37838),i=s(1359),n=s(18815),o=s(34069),l=s(62923),d=s(31770);let u=(0,o.w)({title:"Cubent Units Usage",description:"View your VS Code extension usage statistics and analytics."}),c=async()=>{let{userId:e}=await (0,a.j)(),t=await (0,i.N)();e&&t||(0,l.redirect)("/sign-in");let s=await n.database.user.findUnique({where:{clerkId:e},select:{id:!0,cubentUnitsUsed:!0,cubentUnitsLimit:!0,subscriptionTier:!0,usageMetrics:{orderBy:{date:"desc"},take:30,select:{date:!0,cubentUnitsUsed:!0,requestsMade:!0}}}});s||(s=await n.database.user.create({data:{clerkId:e,email:t.emailAddresses[0]?.emailAddress||"",name:`${t.firstName||""} ${t.lastName||""}`.trim()||null,picture:t.imageUrl},select:{id:!0,cubentUnitsUsed:!0,cubentUnitsLimit:!0,subscriptionTier:!0,usageMetrics:{orderBy:{date:"desc"},take:30,select:{date:!0,cubentUnitsUsed:!0,requestsMade:!0}}}}));let o=await n.database.usageAnalytics.count({where:{userId:s.id}}),u=s.cubentUnitsUsed&&s.cubentUnitsUsed>0||o>0,c={totalCubentUnits:u?s.cubentUnitsUsed||0:12.5,totalMessages:u?o:45,userLimit:s.cubentUnitsLimit||50,subscriptionTier:s.subscriptionTier||"free_trial",chartData:u?s.usageMetrics:(()=>{let e=[],t=new Date;for(let s=29;s>=0;s--){let r=new Date(t);r.setDate(t.getDate()-s),e.push({date:r,cubentUnitsUsed:2*Math.random()+.1,requestsMade:Math.floor(5*Math.random())+1})}return e})(),user:{name:t.firstName||"User",email:t.emailAddresses[0]?.emailAddress||"",picture:t.imageUrl},isDemo:!u};return(0,r.jsx)(d.UsageAnalytics,{initialData:c})}},57975:e=>{"use strict";e.exports=require("node:util")},58940:(e,t,s)=>{"use strict";s.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>o,Zp:()=>i,aR:()=>n});var r=s(99730);s(57752);var a=s(83590);function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}},59988:(e,t,s)=>{"use strict";s.r(t),s.d(t,{"7fa248ee4cee001992d543e3927d536ddea63c121c":()=>r.ai,"7fd10f19b29f8e8b2951e0bb60d9466e540ba24937":()=>r.ot,"7fe80fb1c9bdcbbae5a7da0586440a249dd4fb207a":()=>a.y,"7ffdf714159b7e9cad55b4d3d168d12a9fa0cc1b9f":()=>r.at});var r=s(54841),a=s(44089)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64959:(e,t,s)=>{"use strict";var r=s(57752),a="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=r.useState,n=r.useEffect,o=r.useLayoutEffect,l=r.useDebugValue;function d(e){var t=e.getSnapshot;e=e.value;try{var s=t();return!a(e,s)}catch(e){return!0}}var u="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var s=t(),r=i({inst:{value:s,getSnapshot:t}}),a=r[0].inst,u=r[1];return o(function(){a.value=s,a.getSnapshot=t,d(a)&&u({inst:a})},[e,s,t]),n(function(){return d(a)&&u({inst:a}),e(function(){d(a)&&u({inst:a})})},[e]),l(s),s};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:u},71265:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19161).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},73024:e=>{"use strict";e.exports=require("node:fs")},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75448:(e,t,s)=>{Promise.resolve().then(s.bind(s,27559))},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},80481:e=>{"use strict";e.exports=require("node:readline")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},87785:(e,t,s)=>{"use strict";s.d(t,{E:()=>l});var r=s(99730);s(57752);var a=s(58576),i=s(72795),n=s(83590);let o=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:s=!1,...i}){let l=s?a.DX:"span";return(0,r.jsx)(l,{"data-slot":"badge",className:(0,n.cn)(o({variant:t}),e),...i})}},91645:e=>{"use strict";e.exports=require("net")},94175:e=>{"use strict";e.exports=require("stream/web")},94735:e=>{"use strict";e.exports=require("events")},95617:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.default,__next_app__:()=>u,pages:()=>d,routeModule:()=>c,tree:()=>l});var r=s(57864),a=s(94327),i=s(70814),n=s(17984),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let l={children:["",{children:["(authenticated)",{children:["profile",{children:["usage",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,57416)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\profile\\usage\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,16703)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,52945,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,56940))).default(e)],apple:[async e=>(await Promise.resolve().then(s.bind(s,54526))).default(e)],openGraph:[async e=>(await Promise.resolve().then(s.bind(s,36334))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,29622)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(s.bind(s,70814)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,52945,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,56940))).default(e)],apple:[async e=>(await Promise.resolve().then(s.bind(s,54526))).default(e)],openGraph:[async e=>(await Promise.resolve().then(s.bind(s,36334))).default(e)],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\profile\\usage\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},c=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(authenticated)/profile/usage/page",pathname:"/profile/usage",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[5319,7911,6239,903,7838,5480,3319,277,2644,7914,5432,4841,8004,1121,864,3781,6648],()=>s(95617));module.exports=r})();
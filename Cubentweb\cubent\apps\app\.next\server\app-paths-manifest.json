{"/_not-found/page": "app/_not-found/page.js", "/api/extension/analytics/route": "app/api/extension/analytics/route.js", "/api/extension/auth/route": "app/api/extension/auth/route.js", "/api/extension/api-keys/route": "app/api/extension/api-keys/route.js", "/api/extension/export/route": "app/api/extension/export/route.js", "/api/extension/connect/route": "app/api/extension/connect/route.js", "/api/extension/generate-key/route": "app/api/extension/generate-key/route.js", "/api/extension/settings/route": "app/api/extension/settings/route.js", "/api/extension/sign-in/route": "app/api/extension/sign-in/route.js", "/api/extension/profile/route": "app/api/extension/profile/route.js", "/api/extension/status/route": "app/api/extension/status/route.js", "/api/extension/subscription/route": "app/api/extension/subscription/route.js", "/api/extension/usage/stats/route": "app/api/extension/usage/stats/route.js", "/api/extension/sessions/route": "app/api/extension/sessions/route.js", "/api/extension/sync/route": "app/api/extension/sync/route.js", "/api/extension/usage/route": "app/api/extension/usage/route.js", "/apple-icon.png/route": "app/apple-icon.png/route.js", "/icon.png/route": "app/icon.png/route.js", "/api/terms/accept/route": "app/api/terms/accept/route.js", "/opengraph-image.png/route": "app/opengraph-image.png/route.js", "/.well-known/vercel/flags/route": "app/.well-known/vercel/flags/route.js", "/api/cron/cleanup-tokens/route": "app/api/cron/cleanup-tokens/route.js", "/api/collaboration/auth/route": "app/api/collaboration/auth/route.js", "/api/extension/login/route": "app/api/extension/login/route.js", "/api/token/route": "app/api/token/route.js", "/(authenticated)/debug-auth/page": "app/(authenticated)/debug-auth/page.js", "/(authenticated)/auth-success/page": "app/(authenticated)/auth-success/page.js", "/(authenticated)/login/page": "app/(authenticated)/login/page.js", "/(authenticated)/profile/page": "app/(authenticated)/profile/page.js", "/(authenticated)/page": "app/(authenticated)/page.js", "/(authenticated)/profile/settings/page": "app/(authenticated)/profile/settings/page.js", "/(authenticated)/profile/extension/page": "app/(authenticated)/profile/extension/page.js", "/(authenticated)/search/page": "app/(authenticated)/search/page.js", "/(authenticated)/webhooks/page": "app/(authenticated)/webhooks/page.js", "/(authenticated)/profile/usage/page": "app/(authenticated)/profile/usage/page.js", "/(unauthenticated)/sign-up/[[...sign-up]]/page": "app/(unauthenticated)/sign-up/[[...sign-up]]/page.js", "/(authenticated)/terms/page": "app/(authenticated)/terms/page.js", "/(unauthenticated)/sign-in/[[...sign-in]]/page": "app/(unauthenticated)/sign-in/[[...sign-in]]/page.js"}
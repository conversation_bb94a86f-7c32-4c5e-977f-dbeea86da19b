(()=>{var e={};e.id=1217,e.ids=[1217],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},13440:e=>{"use strict";e.exports=require("util/types")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},17573:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.default,__next_app__:()=>u,pages:()=>d,routeModule:()=>l,tree:()=>c});var s=r(57864),i=r(94327),n=r(70814),a=r(17984),o={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>a[e]);r.d(t,o);let c={children:["",{children:["(authenticated)",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,49532)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\login\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,16703)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,52945,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,56940))).default(e)],apple:[async e=>(await Promise.resolve().then(r.bind(r,54526))).default(e)],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,36334))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,29622)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,70814)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,52945,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,56940))).default(e)],apple:[async e=>(await Promise.resolve().then(r.bind(r,54526))).default(e)],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,36334))).default(e)],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\login\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},l=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(authenticated)/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19439:(e,t,r)=>{Promise.resolve().then(r.bind(r,36062))},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27687:(e,t,r)=>{Promise.resolve().then(r.bind(r,64734))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36062:(e,t,r)=>{"use strict";r.d(t,{LoginFlow:()=>s});let s=(0,r(6340).registerClientReference)(function(){throw Error("Attempted to call LoginFlow() from the server but LoginFlow is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\login\\components\\login-flow.tsx","LoginFlow")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},49532:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(94752),i=r(37838),n=r(1359),a=r(18815),o=r(62923),c=r(36062);let d=async({searchParams:e})=>{let{userId:t}=await (0,i.j)(),r=await (0,n.N)(),d=await e;if(!d.device_id||!d.state)return(0,s.jsx)("div",{className:"flex min-h-screen items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-red-600",children:"Invalid Request"}),(0,s.jsx)("p",{className:"mt-2 text-gray-600",children:"Missing required parameters: device_id and state"})]})});if(!t||!r){let e=new URL("/sign-in","http://localhost:3000");e.searchParams.set("redirect_url",`/login?device_id=${d.device_id}&state=${d.state}`),(0,o.redirect)(e.toString())}let u=await a.database.user.findUnique({where:{clerkId:t}});return u||(u=await a.database.user.create({data:{clerkId:t,email:r.emailAddresses[0]?.emailAddress||"",name:`${r.firstName||""} ${r.lastName||""}`.trim()||null,picture:r.imageUrl}})),(0,s.jsx)(c.LoginFlow,{deviceId:d.device_id,state:d.state,user:u})}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},54287:e=>{"use strict";e.exports=require("console")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},58940:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>n,aR:()=>a});var s=r(99730);r(57752);var i=r(83590);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,i.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function a({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,i.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,i.cn)("leading-none font-semibold",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,i.cn)("px-6",e),...t})}},59988:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"7fa248ee4cee001992d543e3927d536ddea63c121c":()=>s.ai,"7fd10f19b29f8e8b2951e0bb60d9466e540ba24937":()=>s.ot,"7fe80fb1c9bdcbbae5a7da0586440a249dd4fb207a":()=>i.y,"7ffdf714159b7e9cad55b4d3d168d12a9fa0cc1b9f":()=>s.at});var s=r(54841),i=r(44089)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64734:(e,t,r)=>{"use strict";r.d(t,{LoginFlow:()=>T});var s=r(99730),i=r(74938),n=r(58940),a=r(57752),o=r(46854),c=r(1493),d=r(46769),u=r(58785),l=r(89768),p=r(93147),x=r(86552),h=r(56750),m="Checkbox",[f,b]=(0,c.A)(m),[v,g]=f(m);function w(e){let{__scopeCheckbox:t,checked:r,children:i,defaultChecked:n,disabled:o,form:c,name:d,onCheckedChange:l,required:p,value:x="on",internal_do_not_use_render:h}=e,[f,b]=(0,u.i)({prop:r,defaultProp:n??!1,onChange:l,caller:m}),[g,w]=a.useState(null),[j,y]=a.useState(null),q=a.useRef(!1),k=!g||!!c||!!g.closest("form"),N={checked:f,disabled:o,setChecked:b,control:g,setControl:w,name:d,form:c,value:x,hasConsumerStoppedPropagationRef:q,required:p,defaultChecked:!P(n)&&n,isFormControl:k,bubbleInput:j,setBubbleInput:y};return(0,s.jsx)(v,{scope:t,...N,children:"function"==typeof h?h(N):i})}var j="CheckboxTrigger",y=a.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:r,...i},n)=>{let{control:c,value:u,disabled:l,checked:p,required:x,setControl:m,setChecked:f,hasConsumerStoppedPropagationRef:b,isFormControl:v,bubbleInput:w}=g(j,e),y=(0,o.s)(n,m),q=a.useRef(p);return a.useEffect(()=>{let e=c?.form;if(e){let t=()=>f(q.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[c,f]),(0,s.jsx)(h.sG.button,{type:"button",role:"checkbox","aria-checked":P(p)?"mixed":p,"aria-required":x,"data-state":E(p),"data-disabled":l?"":void 0,disabled:l,value:u,...i,ref:y,onKeyDown:(0,d.m)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,d.m)(r,e=>{f(e=>!!P(e)||!e),w&&v&&(b.current=e.isPropagationStopped(),b.current||e.stopPropagation())})})});y.displayName=j;var q=a.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:i,checked:n,defaultChecked:a,required:o,disabled:c,value:d,onCheckedChange:u,form:l,...p}=e;return(0,s.jsx)(w,{__scopeCheckbox:r,checked:n,defaultChecked:a,disabled:c,required:o,onCheckedChange:u,name:i,form:l,value:d,internal_do_not_use_render:({isFormControl:e})=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(y,{...p,ref:t,__scopeCheckbox:r}),e&&(0,s.jsx)(_,{__scopeCheckbox:r})]})})});q.displayName=m;var k="CheckboxIndicator",N=a.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:i,...n}=e,a=g(k,r);return(0,s.jsx)(x.C,{present:i||P(a.checked)||!0===a.checked,children:(0,s.jsx)(h.sG.span,{"data-state":E(a.checked),"data-disabled":a.disabled?"":void 0,...n,ref:t,style:{pointerEvents:"none",...e.style}})})});N.displayName=k;var C="CheckboxBubbleInput",_=a.forwardRef(({__scopeCheckbox:e,...t},r)=>{let{control:i,hasConsumerStoppedPropagationRef:n,checked:c,defaultChecked:d,required:u,disabled:x,name:m,value:f,form:b,bubbleInput:v,setBubbleInput:w}=g(C,e),j=(0,o.s)(r,w),y=(0,l.Z)(c),q=(0,p.X)(i);a.useEffect(()=>{if(!v)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!n.current;if(y!==c&&e){let r=new Event("click",{bubbles:t});v.indeterminate=P(c),e.call(v,!P(c)&&c),v.dispatchEvent(r)}},[v,y,c,n]);let k=a.useRef(!P(c)&&c);return(0,s.jsx)(h.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:d??k.current,required:u,disabled:x,name:m,value:f,form:b,...t,tabIndex:-1,ref:j,style:{...t.style,...q,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function P(e){return"indeterminate"===e}function E(e){return P(e)?"indeterminate":e?"checked":"unchecked"}_.displayName=C;var R=r(44669),L=r(83590);function F({className:e,...t}){return(0,s.jsx)(q,{"data-slot":"checkbox",className:(0,L.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,s.jsx)(N,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,s.jsx)(R.A,{className:"size-3.5"})})})}var S=r(22683);let T=({deviceId:e,state:t,user:r})=>{let[o,c]=(0,a.useState)(r.termsAccepted),[d,u]=(0,a.useState)(!1),[l,p]=(0,a.useState)(!1),x=async()=>{if(!o)return void S.toast.error("Please accept the Terms of Use to continue");u(!0);try{let s=await fetch("/api/extension/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({deviceId:e,state:t,acceptTerms:!r.termsAccepted})});if(!s.ok){let e=await s.json();throw Error(e.message||"Failed to complete login")}let{token:i,redirectUrl:n}=await s.json();p(!0),S.toast.success("Login successful! You can now return to VS Code."),n&&setTimeout(()=>{window.location.href=n},2e3)}catch(e){console.error("Login error:",e),S.toast.error(e instanceof Error?e.message:"Login failed")}finally{u(!1)}};return l?(0,s.jsx)("div",{className:"flex min-h-screen items-center justify-center p-4",children:(0,s.jsxs)(n.Zp,{className:"w-full max-w-md",children:[(0,s.jsxs)(n.aR,{className:"text-center",children:[(0,s.jsx)(n.ZB,{className:"text-green-600",children:"Login Successful!"}),(0,s.jsx)(n.BT,{children:"Your VS Code extension has been authorized successfully."})]}),(0,s.jsxs)(n.Wu,{className:"text-center",children:[(0,s.jsx)("div",{className:"mb-4",children:(0,s.jsx)("div",{className:"mx-auto h-16 w-16 rounded-full bg-green-100 flex items-center justify-center",children:(0,s.jsx)("svg",{className:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})})}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"You can now return to VS Code. The extension should automatically detect the authorization."})]})]})}):(0,s.jsx)("div",{className:"flex min-h-screen items-center justify-center p-4",children:(0,s.jsxs)(n.Zp,{className:"w-full max-w-md",children:[(0,s.jsxs)(n.aR,{children:[(0,s.jsx)(n.ZB,{children:"Authorize VS Code Extension"}),(0,s.jsxs)(n.BT,{children:["Welcome, ",r.name||r.email,"! Please review and accept our terms to continue."]})]}),(0,s.jsxs)(n.Wu,{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"font-semibold",children:"Terms of Use"}),(0,s.jsxs)("div",{className:"max-h-48 overflow-y-auto rounded border p-4 text-sm",children:[(0,s.jsx)("p",{className:"mb-4",children:"By using the Cubent VS Code extension, you agree to the following terms:"}),(0,s.jsxs)("ul",{className:"list-disc space-y-2 pl-4",children:[(0,s.jsx)("li",{children:"You will use the extension in accordance with our usage policies"}),(0,s.jsx)("li",{children:"You understand that AI-generated code should be reviewed before use"}),(0,s.jsx)("li",{children:"You agree to our data collection and processing practices"}),(0,s.jsx)("li",{children:"You will not use the extension for malicious or harmful purposes"}),(0,s.jsx)("li",{children:'You acknowledge that the service is provided "as is"'})]}),(0,s.jsx)("p",{className:"mt-4 text-xs text-gray-500",children:"For full terms, visit our website's Terms of Service page."})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(F,{id:"terms",checked:o,onCheckedChange:e=>c(e)}),(0,s.jsx)("label",{htmlFor:"terms",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"I accept the Terms of Use"})]}),(0,s.jsx)(i.$,{onClick:x,disabled:!o||d,className:"w-full",children:d?"Authorizing...":"Authorize Extension"}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:["Device ID: ",e.slice(0,8),"..."]})})]})]})})}},73024:e=>{"use strict";e.exports=require("node:fs")},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},80481:e=>{"use strict";e.exports=require("node:readline")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},89768:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var s=r(57752);function i(e){let t=s.useRef({value:e,previous:e});return s.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},91645:e=>{"use strict";e.exports=require("net")},94175:e=>{"use strict";e.exports=require("stream/web")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5319,7911,6239,903,7838,5480,3319,277,2644,7914,3051,4841,8004,864,3781,6648],()=>r(17573));module.exports=s})();
{"version": 3, "middleware": {"/": {"files": ["server/edge-instrumentation.js", "server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "3SyV-ZkRBT_UMfwEDYrUb", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "PsOk9cbLj57mzbP7S5dexg03XmOiex4+AQ0DyfIt1KU=", "__NEXT_PREVIEW_MODE_ID": "b39b3874d89b2915aea9c960c3cc28a8", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "19e1ec59972d5756c31d61b2f202f08eb294df77a7d4ce0b5f1baefde0c2ef0f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "529fa3ffdd718474ac82fdd433566e1d091b8a5bdca4128ff71dfb79156fa531"}}}, "functions": {}, "sortedMiddleware": ["/"]}
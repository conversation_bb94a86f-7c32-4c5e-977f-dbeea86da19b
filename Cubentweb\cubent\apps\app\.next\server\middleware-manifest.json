{"version": 3, "middleware": {"/": {"files": ["server/edge-instrumentation.js", "server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "ZY1Y6rqzGhNbMgyiW6a5f", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "PsOk9cbLj57mzbP7S5dexg03XmOiex4+AQ0DyfIt1KU=", "__NEXT_PREVIEW_MODE_ID": "e7c337fc634d937fd62da541f25f264b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "673b4d3b96225d952ca0186dc70aabcf6dca41cff10a68555751e52b905b87fa", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "e985538bc3730a082c2beb019bcfb02e09e642c0703d9cda1c8f837738233f9e"}}}, "functions": {}, "sortedMiddleware": ["/"]}
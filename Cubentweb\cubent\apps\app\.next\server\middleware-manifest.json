{"version": 3, "middleware": {"/": {"files": ["server/edge-instrumentation.js", "server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "kBae9JMoDezAK9BNmVd2Y", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "PsOk9cbLj57mzbP7S5dexg03XmOiex4+AQ0DyfIt1KU=", "__NEXT_PREVIEW_MODE_ID": "23ccf8e3b8ab97e6fe2f11b748cf268b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "6c988819381a2e05effdc45d6d3b51a3356496999ddca8c05a1e991f6a11a16c", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "265625149352e59afeabb81327fc1e042b1a5b349a1e0e602fdd5673a623090d"}}}, "functions": {}, "sortedMiddleware": ["/"]}
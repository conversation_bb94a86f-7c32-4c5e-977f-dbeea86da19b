exports.id=5432,exports.ids=[5432],exports.modules={673:(e,r,t)=>{"use strict";t.d(r,{F:()=>s});var a=t(35371);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=a.$,s=(e,r)=>t=>{var a;if((null==r?void 0:r.variants)==null)return i(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:s,defaultVariants:o}=r,d=Object.keys(s).map(e=>{let r=null==t?void 0:t[e],a=null==o?void 0:o[e];if(null===r)return null;let i=n(r)||n(a);return s[e][i]}),l=t&&Object.entries(t).reduce((e,r)=>{let[t,a]=r;return void 0===a||(e[t]=a),e},{});return i(e,d,null==r||null==(a=r.compoundVariants)?void 0:a.reduce((e,r)=>{let{class:t,className:a,...n}=r;return Object.entries(n).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...o,...l}[r]):({...o,...l})[r]===t})?[...e,t,a]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},24700:(e,r,t)=>{"use strict";t.d(r,{$:()=>d});var a=t(94752);t(23233);var n=t(30409),i=t(673),s=t(58559);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:r,size:t,asChild:i=!1,...d}){let l=i?n.DX:"button";return(0,a.jsx)(l,{"data-slot":"button",className:(0,s.cn)(o({variant:r,size:t,className:e})),...d})}},24767:(e,r,t)=>{"use strict";t.d(r,{A:()=>u});var a=t(23233);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),s=e=>{let r=i(e);return r.charAt(0).toUpperCase()+r.slice(1)},o=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim(),d=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,a.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:n,className:i="",children:s,iconNode:c,...u},f)=>(0,a.createElement)("svg",{ref:f,...l,width:r,height:r,stroke:e,strokeWidth:n?24*Number(t)/Number(r):t,className:o("lucide",i),...!s&&!d(u)&&{"aria-hidden":"true"},...u},[...c.map(([e,r])=>(0,a.createElement)(e,r)),...Array.isArray(s)?s:[s]])),u=(e,r)=>{let t=(0,a.forwardRef)(({className:t,...i},d)=>(0,a.createElement)(c,{ref:d,iconNode:r,className:o(`lucide-${n(s(e))}`,`lucide-${e}`,t),...i}));return t.displayName=s(e),t}},30409:(e,r,t)=>{"use strict";t.d(r,{DX:()=>s});var a=t(23233);function n(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}var i=t(94752),s=function(e){let r=function(e){let r=a.forwardRef((e,r)=>{let{children:t,...i}=e;if(a.isValidElement(t)){var s;let e,o,d=(s=t,(o=(e=Object.getOwnPropertyDescriptor(s.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.ref:(o=(e=Object.getOwnPropertyDescriptor(s,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.props.ref:s.props.ref||s.ref),l=function(e,r){let t={...r};for(let a in r){let n=e[a],i=r[a];/^on[A-Z]/.test(a)?n&&i?t[a]=(...e)=>{let r=i(...e);return n(...e),r}:n&&(t[a]=n):"style"===a?t[a]={...n,...i}:"className"===a&&(t[a]=[n,i].filter(Boolean).join(" "))}return{...e,...t}}(i,t.props);return t.type!==a.Fragment&&(l.ref=r?function(...e){return r=>{let t=!1,a=e.map(e=>{let a=n(e,r);return t||"function"!=typeof a||(t=!0),a});if(t)return()=>{for(let r=0;r<a.length;r++){let t=a[r];"function"==typeof t?t():n(e[r],null)}}}}(r,d):d),a.cloneElement(t,l)}return a.Children.count(t)>1?a.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=a.forwardRef((e,t)=>{let{children:n,...s}=e,o=a.Children.toArray(n),l=o.find(d);if(l){let e=l.props.children,n=o.map(r=>r!==l?r:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,i.jsx)(r,{...s,ref:t,children:a.isValidElement(e)?a.cloneElement(e,void 0,n):null})}return(0,i.jsx)(r,{...s,ref:t,children:n})});return t.displayName=`${e}.Slot`,t}("Slot"),o=Symbol("radix.slottable");function d(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},34069:(e,r,t)=>{"use strict";t.d(r,{w:()=>d});var a=t(81121),n=t.n(a);let i="next-forge",s={name:"Vercel",url:"https://vercel.com/"},o=process.env.VERCEL_PROJECT_PRODUCTION_URL,d=({title:e,description:r,image:t,...a})=>{let d=`${e} | ${i}`,l={title:d,description:r,applicationName:i,metadataBase:o?new URL(`https://${o}`):void 0,authors:[s],creator:s.name,formatDetection:{telephone:!1},appleWebApp:{capable:!0,statusBarStyle:"default",title:d},openGraph:{title:d,description:r,type:"website",siteName:i,locale:"en_US"},publisher:"Vercel",twitter:{card:"summary_large_image",creator:"@vercel"}},c=n()(l,a);return t&&c.openGraph&&(c.openGraph.images=[{url:t,width:1200,height:630,alt:e}]),c}},43131:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(24767).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},44793:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(24767).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},46954:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>i,aR:()=>s});var a=t(94752);t(23233);var n=t(58559);function i({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...r})}function s({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function o({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...r})}function d({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...r})}function l({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...r})}},49499:(e,r,t)=>{let{createProxy:a}=t(20867);e.exports=a("C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f\\node_modules\\next\\dist\\client\\app-dir\\link.js")},59988:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"7fa248ee4cee001992d543e3927d536ddea63c121c":()=>a.ai,"7fd10f19b29f8e8b2951e0bb60d9466e540ba24937":()=>a.ot,"7fe80fb1c9bdcbbae5a7da0586440a249dd4fb207a":()=>n.y,"7ffdf714159b7e9cad55b4d3d168d12a9fa0cc1b9f":()=>a.at});var a=t(54841),n=t(44089)},70483:(e,r,t)=>{"use strict";t.d(r,{E:()=>d});var a=t(94752);t(23233);var n=t(30409),i=t(673),s=t(58559);let o=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:r,asChild:t=!1,...i}){let d=t?n.DX:"span";return(0,a.jsx)(d,{"data-slot":"badge",className:(0,s.cn)(o({variant:r}),e),...i})}}};
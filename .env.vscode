# =============================================================================
# CUBENT DEVELOPMENT ENVIRONMENT CONFIGURATION
# =============================================================================
# This file contains environment variables for local development of both:
# - Cubent.dev (VS Code Extension)
# - Cubentweb (Next.js Website/API)
# 
# Based on Next Forge template: https://www.next-forge.com/docs
# =============================================================================

# =============================================================================
# SERVER-SIDE ENVIRONMENT VARIABLES
# =============================================================================

# -----------------------------------------------------------------------------
# Authentication & User Management (Clerk)
# -----------------------------------------------------------------------------
CLERK_SECRET_KEY="sk_test_Gy9j2NWYXEh5gvztI6EMqo2MZsgfHWYHLwfFTLLOim"
CLERK_WEBHOOK_SECRET="whsec_placeholder_development_only"

# -----------------------------------------------------------------------------
# Database Configuration (Neon PostgreSQL)
# -----------------------------------------------------------------------------
DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"

# -----------------------------------------------------------------------------
# Email Service (Resend)
# -----------------------------------------------------------------------------
RESEND_FROM="<EMAIL>"
RESEND_TOKEN="re_5vhEiMML_7bVPRtnLkk3evtKBU28Kw2gZ"

# -----------------------------------------------------------------------------
# Payment Processing (Stripe)
# -----------------------------------------------------------------------------
STRIPE_SECRET_KEY="sk_test_51RacKARvLc5HHcqOWimagNKG7tHk1dOG8x4PR8DQu2GKmOS5ok2mIX5A7QqdkifpxPvmKp0lOYwIR0ud0BMA2jZa00IOtQj8Wg"
STRIPE_WEBHOOK_SECRET="whsec_stripe_placeholder_development_only"

# -----------------------------------------------------------------------------
# Monitoring & Logging (BetterStack)
# -----------------------------------------------------------------------------
BETTERSTACK_API_KEY="************************"
BETTERSTACK_URL="https://placeholder.betterstack.com"

# -----------------------------------------------------------------------------
# Feature Flags
# -----------------------------------------------------------------------------
FLAGS_SECRET=""

# -----------------------------------------------------------------------------
# Security & Rate Limiting (Arcjet)
# -----------------------------------------------------------------------------
ARCJET_KEY="ajkey_01jxwcpseafd39jw0h25fda53z"

# -----------------------------------------------------------------------------
# Real-time Collaboration (Liveblocks)
# -----------------------------------------------------------------------------
LIVEBLOCKS_SECRET="sk_test_EkRdbKV0kFsUOMMffwmvf8T2o-ow_o3K-sJzwBGsuT4"

# -----------------------------------------------------------------------------
# Content Management (BaseHub)
# -----------------------------------------------------------------------------
BASEHUB_TOKEN="bshb_pk_zsskbo2563n1hgdhrl47yyttoh3pu7e8orawmx24z9q32rc66tkgw7ifani5l6vi"

# -----------------------------------------------------------------------------
# Deployment Configuration
# -----------------------------------------------------------------------------
VERCEL_PROJECT_PRODUCTION_URL="http://localhost:3002"

# -----------------------------------------------------------------------------
# Notifications (Knock)
# -----------------------------------------------------------------------------
KNOCK_API_KEY=""
KNOCK_FEED_CHANNEL_ID=""

# -----------------------------------------------------------------------------
# Development Tools (Optional)
# -----------------------------------------------------------------------------
# SVIX_TOKEN="sk_placeholder_development_only"

# =============================================================================
# CLIENT-SIDE ENVIRONMENT VARIABLES (NEXT_PUBLIC_*)
# =============================================================================

# -----------------------------------------------------------------------------
# Authentication (Clerk Public Keys)
# -----------------------------------------------------------------------------
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY="pk_test_YXdhcmUtZ3JvdXBlci03OS5jbGVyay5hY2NvdW50cy5kZXYk"
NEXT_PUBLIC_CLERK_SIGN_IN_URL="/https://aware-grouper-79.accounts.dev/sign-in"
NEXT_PUBLIC_CLERK_SIGN_UP_URL="/https://aware-grouper-79.accounts.dev/sign-up"
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL="/"
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL="/"

# -----------------------------------------------------------------------------
# Analytics & Tracking
# -----------------------------------------------------------------------------
NEXT_PUBLIC_GA_MEASUREMENT_ID="G-PLACEHOLDER123"
NEXT_PUBLIC_POSTHOG_KEY="phc_IIiOB59nWFyFh8azKXcqkOucMA9x5jTUYPTEDx2ccP9"
NEXT_PUBLIC_POSTHOG_HOST="https://eu.i.posthog.com"

# -----------------------------------------------------------------------------
# Application URLs (Development)
# -----------------------------------------------------------------------------
NEXT_PUBLIC_DOCS_URL="http://localhost:3004"
NEXT_PUBLIC_APP_URL="http://localhost:3000"
NEXT_PUBLIC_WEB_URL="http://localhost:3001"

# -----------------------------------------------------------------------------
# Payment Processing (Stripe Public Key)
# -----------------------------------------------------------------------------
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_51RacKARvLc5HHcqOqUhGjV4F4oG97zsJCyQ3Xa7fTHaDXU3IijB7EZtzcSLX2FfACxLc4ZWD9i74UsX6VLlVPyGt00rCPNwoOg"

# =============================================================================
# CUBENT EXTENSION SPECIFIC VARIABLES
# =============================================================================

# -----------------------------------------------------------------------------
# Extension API Configuration
# -----------------------------------------------------------------------------
# For production: use the live webapp URL
# For development: use localhost
ROO_CODE_API_URL="https://app-cubent.vercel.app"
CUBENT_API_BASE_URL="https://app-cubent.vercel.app"

# -----------------------------------------------------------------------------
# Extension Development
# -----------------------------------------------------------------------------
NODE_ENV="development"
CUBENT_DEBUG="true"
EXTENSION_VERSION="3.19.3"

# -----------------------------------------------------------------------------
# AI Provider Configuration (for testing)
# -----------------------------------------------------------------------------
# Add your AI provider keys here for extension testing
# OPENAI_API_KEY=""
# ANTHROPIC_API_KEY=""
# GOOGLE_API_KEY=""

# =============================================================================
# DEVELOPMENT PORTS & SERVICES
# =============================================================================

# Port Configuration for Next Forge Apps:
# - App (Main Application): 3000
# - Web (Marketing Site): 3001  
# - API (Backend Services): 3002
# - Docs (Documentation): 3004
# - Storybook (Component Library): 6006

# =============================================================================
# USAGE INSTRUCTIONS
# =============================================================================
# 
# 1. Copy this file to your project roots:
#    - Cubentweb/cubent/.env.local
#    - Cubent.dev/.env (if needed for extension development)
#
# 2. Update any placeholder values with your actual keys
#
# 3. For production deployment, use Vercel environment variables
#
# 4. Never commit this file to version control
#
# =============================================================================

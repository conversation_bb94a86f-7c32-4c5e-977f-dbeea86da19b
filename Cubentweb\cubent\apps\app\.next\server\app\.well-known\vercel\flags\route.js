(()=>{var e={};e.id=8003,e.ids=[8003],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19257:(e,t,r)=>{"use strict";r.r(t),r.d(t,{showBetaFeature:()=>u});var s=r(51153);let n=(0,r(28364).H)(),i=n.NEXT_PUBLIC_POSTHOG_KEY&&n.NEXT_PUBLIC_POSTHOG_HOST?new s.f2(n.NEXT_PUBLIC_POSTHOG_KEY,{host:n.NEXT_PUBLIC_POSTHOG_HOST,flushAt:1,flushInterval:0}):null;var o=r(37838),a=r(8392);let u=(e=>(0,a.Jt)({key:e,defaultValue:!1,async decide(){let{userId:t}=await (0,o.j)();return t?(i?await i.isFeatureEnabled(e,t):null)??this.defaultValue:this.defaultValue}}))("showBetaFeature")},26142:(e,t,r)=>{"use strict";e.exports=r(44870)},28364:(e,t,r)=>{"use strict";r.d(t,{H:()=>i});var s=r(71166),n=r(25);let i=()=>(0,s.w)({client:{NEXT_PUBLIC_POSTHOG_KEY:n.z.string().startsWith("phc_").optional(),NEXT_PUBLIC_POSTHOG_HOST:n.z.string().url().optional(),NEXT_PUBLIC_GA_MEASUREMENT_ID:n.z.string().startsWith("G-").optional()},runtimeEnv:{NEXT_PUBLIC_POSTHOG_KEY:"phc_IIiOB59nWFyFh8azKXcqkOucMA9x5jTUYPTEDx2ccP9",NEXT_PUBLIC_POSTHOG_HOST:"https://eu.i.posthog.com",NEXT_PUBLIC_GA_MEASUREMENT_ID:"G-PLACEHOLDER123"}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35657:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>_,routeModule:()=>p,serverHooks:()=>f,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>v});var s={};r.r(s),r.d(s,{GET:()=>c});var n=r(26142),i=r(94327),o=r(34862),a=r(26818),u=r(26239),l=r(19257);let c=async e=>{if(!await (0,a.uX)(e.headers.get("Authorization")))return u.NextResponse.json(null,{status:401});let t=Object.fromEntries(Object.values(l).map(e=>[e.key,{origin:e.origin,description:e.description,options:e.options}]));return u.NextResponse.json({definitions:t})},p=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/.well-known/vercel/flags/route",pathname:"/.well-known/vercel/flags",filename:"route",bundlePath:"app/.well-known/vercel/flags/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\.well-known\\vercel\\flags\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:v,serverHooks:f}=p;function _(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:v})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},57975:e=>{"use strict";e.exports=require("node:util")},59986:(e,t,r)=>{"use strict";function s(e,t){if(e instanceof Promise)throw Error(t)}function n(e){let t=e.runtimeEnvStrict??e.runtimeEnv??process.env;if(e.emptyStringAsUndefined)for(let[e,r]of Object.entries(t))""===r&&delete t[e];if(e.skipValidation)return t;let r="object"==typeof e.client?e.client:{},n="object"==typeof e.server?e.server:{},i="object"==typeof e.shared?e.shared:{},o=e.isServer??("undefined"==typeof window||"Deno"in window),a=o?{...n,...i,...r}:{...r,...i},u=e.createFinalSchema?.(a,o)["~standard"].validate(t)??function(e,t){let r={},n=[];for(let i in e){let o=e[i]["~standard"].validate(t[i]);if(s(o,`Validation must be synchronous, but ${i} returned a Promise.`),o.issues){n.push(...o.issues.map(e=>({...e,path:[i,...e.path??[]]})));continue}r[i]=o.value}return n.length?{issues:n}:{value:r}}(a,t);s(u,"Validation must be synchronous");let l=e.onValidationError??(e=>{throw console.error("❌ Invalid environment variables:",e),Error("Invalid environment variables")}),c=e.onInvalidAccess??(()=>{throw Error("❌ Attempted to access a server-side environment variable on the client")});if(u.issues)return l(u.issues);let p=t=>!e.clientPrefix||!t.startsWith(e.clientPrefix)&&!(t in i),d=e=>o||!p(e),v=e=>"__esModule"===e||"$$typeof"===e;return new Proxy(Object.assign((e.extends??[]).reduce((e,t)=>Object.assign(e,t),{}),u.value),{get(e,t){if("string"==typeof t&&!v(t))return d(t)?Reflect.get(e,t):c(t)}})}r.d(t,{w:()=>n})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71166:(e,t,r)=>{"use strict";r.d(t,{w:()=>n});var s=r(59986);function n(e){let t="object"==typeof e.client?e.client:{},r="object"==typeof e.server?e.server:{},n=e.shared,i=e.runtimeEnv?e.runtimeEnv:{...process.env,...e.experimental__runtimeEnv};return(0,s.w)({...e,shared:n,client:t,server:r,clientPrefix:"NEXT_PUBLIC_",runtimeEnv:i})}},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},80481:e=>{"use strict";e.exports=require("node:readline")},84297:e=>{"use strict";e.exports=require("async_hooks")},89259:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5319,7911,6239,903,7838,3051],()=>r(35657));module.exports=s})();
(()=>{var e={};e.id=2936,e.ids=[2936],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},13440:e=>{"use strict";e.exports=require("util/types")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},23167:(e,t,r)=>{"use strict";r.d(t,{H:()=>i});var s=r(71166),n=r(25);let i=()=>(0,s.w)({server:{DATABASE_URL:n.z.string().url()},runtimeEnv:{DATABASE_URL:process.env.DATABASE_URL}})},23633:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(6340).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\2 FOLDERS FOR CUBENT\\\\Cubentweb\\\\cubent\\\\apps\\\\app\\\\app\\\\(authenticated)\\\\debug-auth\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\debug-auth\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30095:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(99730),n=r(74938),i=r(58940),o=r(57752);function a(){let[e,t]=(0,o.useState)([]),r=e=>{let r=new Date().toLocaleTimeString();t(t=>[...t,`[${r}] ${e}`])};return(0,s.jsxs)("div",{className:"container mx-auto py-8 space-y-6",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Authentication Flow Debugger"}),(0,s.jsx)("p",{className:"text-muted-foreground mt-2",children:"Test and debug extension authentication flows"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{children:(0,s.jsx)(i.ZB,{children:"Device OAuth Flow"})}),(0,s.jsxs)(i.Wu,{className:"space-y-4",children:[(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:'Tests the primary authentication flow used by the "Connect to Cubent Cloud" button.'}),(0,s.jsx)(n.$,{onClick:()=>{r("Testing Device OAuth Flow...");let e="test-device-"+Math.random().toString(36).substr(2,9),t="test-state-"+Math.random().toString(36).substr(2,9),s=`/sign-in?device_id=${e}&state=${t}`;r(`Generated URL: ${s}`),r("Opening in new tab..."),window.open(s,"_blank")},className:"w-full",children:"Test Device OAuth"})]})]}),(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{children:(0,s.jsx)(i.ZB,{children:"Legacy Flow"})}),(0,s.jsxs)(i.Wu,{className:"space-y-4",children:[(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:'Tests the legacy authentication flow used by the "Sign In (Legacy)" button.'}),(0,s.jsx)(n.$,{onClick:()=>{r("Testing Legacy Flow...");let e="test-state-"+Math.random().toString(36).substr(2,9),t=`/api/extension/sign-in?state=${e}&auth_redirect=${encodeURIComponent("vscode://cubent.cubent/auth")}`;r(`Generated URL: ${t}`),r("Opening in new tab..."),window.open(t,"_blank")},className:"w-full",children:"Test Legacy Flow"})]})]}),(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{children:(0,s.jsx)(i.ZB,{children:"Direct Login"})}),(0,s.jsxs)(i.Wu,{className:"space-y-4",children:[(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Tests direct access to the login page with device parameters."}),(0,s.jsx)(n.$,{onClick:()=>{r("Testing Direct Login Page...");let e="test-device-"+Math.random().toString(36).substr(2,9),t="test-state-"+Math.random().toString(36).substr(2,9),s=`/login?device_id=${e}&state=${t}`;r(`Generated URL: ${s}`),r("Opening in new tab..."),window.open(s,"_blank")},className:"w-full",children:"Test Direct Login"})]})]}),(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{children:(0,s.jsx)(i.ZB,{children:"Auth Success"})}),(0,s.jsxs)(i.Wu,{className:"space-y-4",children:[(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Tests the auth success page redirect functionality."}),(0,s.jsx)(n.$,{onClick:()=>{r("Testing Auth Success Page...");let e=`/auth-success?redirect_url=${encodeURIComponent("vscode://cubent.cubent/auth/callback?token=test-token&state=test-state")}`;r(`Generated URL: ${e}`),r("Opening in new tab..."),window.open(e,"_blank")},className:"w-full",children:"Test Auth Success"})]})]})]}),(0,s.jsxs)(i.Zp,{children:[(0,s.jsxs)(i.aR,{className:"flex flex-row items-center justify-between",children:[(0,s.jsx)(i.ZB,{children:"Debug Logs"}),(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>t([]),children:"Clear Logs"})]}),(0,s.jsx)(i.Wu,{children:(0,s.jsx)("div",{className:"bg-muted p-4 rounded-md max-h-64 overflow-y-auto",children:0===e.length?(0,s.jsx)("p",{className:"text-muted-foreground text-sm",children:"No logs yet. Click a test button to start."}):(0,s.jsx)("div",{className:"space-y-1",children:e.map((e,t)=>(0,s.jsx)("div",{className:"text-sm font-mono",children:e},t))})})})]}),(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{children:(0,s.jsx)(i.ZB,{children:"Current Environment"})}),(0,s.jsx)(i.Wu,{children:(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Current URL:"}),(0,s.jsx)("br",{}),(0,s.jsx)("code",{className:"text-xs",children:"N/A"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"User Agent:"}),(0,s.jsx)("br",{}),(0,s.jsx)("code",{className:"text-xs",children:"N/A"})]})]})})]})]})}},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41204:e=>{"use strict";e.exports=require("string_decoder")},41472:(e,t,r)=>{Promise.resolve().then(r.bind(r,30095))},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},47547:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.default,__next_app__:()=>d,pages:()=>u,routeModule:()=>l,tree:()=>c});var s=r(57864),n=r(94327),i=r(70814),o=r(17984),a={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>o[e]);r.d(t,a);let c={children:["",{children:["(authenticated)",{children:["debug-auth",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,23633)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\debug-auth\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,16703)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,52945,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,56940))).default(e)],apple:[async e=>(await Promise.resolve().then(r.bind(r,54526))).default(e)],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,36334))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,29622)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,70814)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,52945,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,56940))).default(e)],apple:[async e=>(await Promise.resolve().then(r.bind(r,54526))).default(e)],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,36334))).default(e)],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\debug-auth\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},l=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(authenticated)/debug-auth/page",pathname:"/debug-auth",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},54287:e=>{"use strict";e.exports=require("console")},54624:(e,t,r)=>{Promise.resolve().then(r.bind(r,23633))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},58940:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>u,ZB:()=>a,Zp:()=>i,aR:()=>o});var s=r(99730);r(57752);var n=r(83590);function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function a({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t})}function u({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...t})}},59986:(e,t,r)=>{"use strict";function s(e,t){if(e instanceof Promise)throw Error(t)}function n(e){let t=e.runtimeEnvStrict??e.runtimeEnv??process.env;if(e.emptyStringAsUndefined)for(let[e,r]of Object.entries(t))""===r&&delete t[e];if(e.skipValidation)return t;let r="object"==typeof e.client?e.client:{},n="object"==typeof e.server?e.server:{},i="object"==typeof e.shared?e.shared:{},o=e.isServer??("undefined"==typeof window||"Deno"in window),a=o?{...n,...i,...r}:{...r,...i},c=e.createFinalSchema?.(a,o)["~standard"].validate(t)??function(e,t){let r={},n=[];for(let i in e){let o=e[i]["~standard"].validate(t[i]);if(s(o,`Validation must be synchronous, but ${i} returned a Promise.`),o.issues){n.push(...o.issues.map(e=>({...e,path:[i,...e.path??[]]})));continue}r[i]=o.value}return n.length?{issues:n}:{value:r}}(a,t);s(c,"Validation must be synchronous");let u=e.onValidationError??(e=>{throw console.error("❌ Invalid environment variables:",e),Error("Invalid environment variables")}),d=e.onInvalidAccess??(()=>{throw Error("❌ Attempted to access a server-side environment variable on the client")});if(c.issues)return u(c.issues);let l=t=>!e.clientPrefix||!t.startsWith(e.clientPrefix)&&!(t in i),p=e=>o||!l(e),x=e=>"__esModule"===e||"$$typeof"===e;return new Proxy(Object.assign((e.extends??[]).reduce((e,t)=>Object.assign(e,t),{}),c.value),{get(e,t){if("string"==typeof t&&!x(t))return p(t)?Reflect.get(e,t):d(t)}})}r.d(t,{w:()=>n})},59988:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"7fa248ee4cee001992d543e3927d536ddea63c121c":()=>s.ai,"7fd10f19b29f8e8b2951e0bb60d9466e540ba24937":()=>s.ot,"7fe80fb1c9bdcbbae5a7da0586440a249dd4fb207a":()=>n.y,"7ffdf714159b7e9cad55b4d3d168d12a9fa0cc1b9f":()=>s.at});var s=r(54841),n=r(44089)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71166:(e,t,r)=>{"use strict";r.d(t,{w:()=>n});var s=r(59986);function n(e){let t="object"==typeof e.client?e.client:{},r="object"==typeof e.server?e.server:{},n=e.shared,i=e.runtimeEnv?e.runtimeEnv:{...process.env,...e.experimental__runtimeEnv};return(0,s.w)({...e,shared:n,client:t,server:r,clientPrefix:"NEXT_PUBLIC_",runtimeEnv:i})}},73024:e=>{"use strict";e.exports=require("node:fs")},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},91645:e=>{"use strict";e.exports=require("net")},94175:e=>{"use strict";e.exports=require("stream/web")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5319,7911,6239,903,7838,3319,277,2644,7914,3051,4841,8004,3781,6648],()=>r(47547));module.exports=s})();
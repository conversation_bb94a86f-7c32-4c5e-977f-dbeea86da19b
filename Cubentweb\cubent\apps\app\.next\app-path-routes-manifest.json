{"/_not-found/page": "/_not-found", "/api/extension/analytics/route": "/api/extension/analytics", "/api/extension/auth/route": "/api/extension/auth", "/api/extension/api-keys/route": "/api/extension/api-keys", "/api/extension/connect/route": "/api/extension/connect", "/api/extension/profile/route": "/api/extension/profile", "/api/extension/generate-key/route": "/api/extension/generate-key", "/api/extension/export/route": "/api/extension/export", "/api/extension/settings/route": "/api/extension/settings", "/api/extension/status/route": "/api/extension/status", "/api/extension/sessions/route": "/api/extension/sessions", "/api/extension/subscription/route": "/api/extension/subscription", "/api/extension/usage/stats/route": "/api/extension/usage/stats", "/api/extension/sign-in/route": "/api/extension/sign-in", "/api/extension/sync/route": "/api/extension/sync", "/api/extension/usage/route": "/api/extension/usage", "/api/terms/accept/route": "/api/terms/accept", "/icon.png/route": "/icon.png", "/apple-icon.png/route": "/apple-icon.png", "/opengraph-image.png/route": "/opengraph-image.png", "/.well-known/vercel/flags/route": "/.well-known/vercel/flags", "/api/cron/cleanup-tokens/route": "/api/cron/cleanup-tokens", "/api/collaboration/auth/route": "/api/collaboration/auth", "/api/extension/login/route": "/api/extension/login", "/api/token/route": "/api/token", "/(authenticated)/auth-success/page": "/auth-success", "/(authenticated)/login/page": "/login", "/(authenticated)/profile/usage/page": "/profile/usage", "/(authenticated)/debug-auth/page": "/debug-auth", "/(authenticated)/profile/extension/page": "/profile/extension", "/(authenticated)/search/page": "/search", "/(authenticated)/page": "/", "/(authenticated)/webhooks/page": "/webhooks", "/(unauthenticated)/sign-up/[[...sign-up]]/page": "/sign-up/[[...sign-up]]", "/(authenticated)/profile/settings/page": "/profile/settings", "/(authenticated)/terms/page": "/terms", "/(unauthenticated)/sign-in/[[...sign-in]]/page": "/sign-in/[[...sign-in]]", "/(authenticated)/profile/page": "/profile"}
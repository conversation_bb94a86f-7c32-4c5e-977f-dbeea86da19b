{"/_not-found/page": "/_not-found", "/api/extension/analytics/route": "/api/extension/analytics", "/api/extension/auth/route": "/api/extension/auth", "/api/extension/api-keys/route": "/api/extension/api-keys", "/api/extension/export/route": "/api/extension/export", "/api/extension/connect/route": "/api/extension/connect", "/api/extension/generate-key/route": "/api/extension/generate-key", "/api/extension/settings/route": "/api/extension/settings", "/api/extension/sign-in/route": "/api/extension/sign-in", "/api/extension/profile/route": "/api/extension/profile", "/api/extension/status/route": "/api/extension/status", "/api/extension/subscription/route": "/api/extension/subscription", "/api/extension/usage/stats/route": "/api/extension/usage/stats", "/api/extension/sessions/route": "/api/extension/sessions", "/api/extension/sync/route": "/api/extension/sync", "/api/extension/usage/route": "/api/extension/usage", "/apple-icon.png/route": "/apple-icon.png", "/icon.png/route": "/icon.png", "/api/terms/accept/route": "/api/terms/accept", "/opengraph-image.png/route": "/opengraph-image.png", "/.well-known/vercel/flags/route": "/.well-known/vercel/flags", "/api/cron/cleanup-tokens/route": "/api/cron/cleanup-tokens", "/api/collaboration/auth/route": "/api/collaboration/auth", "/api/extension/login/route": "/api/extension/login", "/api/token/route": "/api/token", "/(authenticated)/debug-auth/page": "/debug-auth", "/(authenticated)/auth-success/page": "/auth-success", "/(authenticated)/login/page": "/login", "/(authenticated)/profile/page": "/profile", "/(authenticated)/page": "/", "/(authenticated)/profile/settings/page": "/profile/settings", "/(authenticated)/profile/extension/page": "/profile/extension", "/(authenticated)/search/page": "/search", "/(authenticated)/webhooks/page": "/webhooks", "/(authenticated)/profile/usage/page": "/profile/usage", "/(unauthenticated)/sign-up/[[...sign-up]]/page": "/sign-up/[[...sign-up]]", "/(authenticated)/terms/page": "/terms", "/(unauthenticated)/sign-in/[[...sign-in]]/page": "/sign-in/[[...sign-in]]"}
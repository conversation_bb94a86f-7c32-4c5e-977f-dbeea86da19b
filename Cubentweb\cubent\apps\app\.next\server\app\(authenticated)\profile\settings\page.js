(()=>{var e={};e.id=7167,e.ids=[7167],e.modules={673:(e,t,r)=>{"use strict";r.d(t,{F:()=>i});var s=r(35371);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=s.$,i=(e,t)=>r=>{var s;if((null==t?void 0:t.variants)==null)return a(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:o}=t,l=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],s=null==o?void 0:o[e];if(null===t)return null;let a=n(t)||n(s);return i[e][a]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return a(e,l,null==t||null==(s=t.compoundVariants)?void 0:s.reduce((e,t)=>{let{class:r,className:s,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...o,...d}[t]):({...o,...d})[t]===r})?[...e,r,s]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},13440:e=>{"use strict";e.exports=require("util/types")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},20671:(e,t,r)=>{Promise.resolve().then(r.bind(r,59380)),Promise.resolve().then(r.t.bind(r,21034,23)),Promise.resolve().then(r.t.bind(r,49499,23)),Promise.resolve().then(r.bind(r,93665))},21820:e=>{"use strict";e.exports=require("os")},24700:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var s=r(94752);r(23233);var n=r(30409),a=r(673),i=r(58559);let o=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:a=!1,...l}){let d=a?n.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,i.cn)(o({variant:t,size:r,className:e})),...l})}},24767:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var s=r(23233);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),i=e=>{let t=a(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:n,className:a="",children:i,iconNode:c,...u},p)=>(0,s.createElement)("svg",{ref:p,...d,width:t,height:t,stroke:e,strokeWidth:n?24*Number(r)/Number(t):r,className:o("lucide",a),...!i&&!l(u)&&{"aria-hidden":"true"},...u},[...c.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(i)?i:[i]])),u=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...a},l)=>(0,s.createElement)(c,{ref:l,iconNode:t,className:o(`lucide-${n(i(e))}`,`lucide-${e}`,r),...a}));return r.displayName=i(e),r}},26483:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>l});var s=r(57864),n=r(94327),a=r(70814),i=r(17984),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let l={children:["",{children:["(authenticated)",{children:["profile",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,93206)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\profile\\settings\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,16703)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,52945,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,56940))).default(e)],apple:[async e=>(await Promise.resolve().then(r.bind(r,54526))).default(e)],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,36334))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,29622)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,70814)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,52945,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,56940))).default(e)],apple:[async e=>(await Promise.resolve().then(r.bind(r,54526))).default(e)],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,36334))).default(e)],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\profile\\settings\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(authenticated)/profile/settings/page",pathname:"/profile/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30409:(e,t,r)=>{"use strict";r.d(t,{DX:()=>i});var s=r(23233);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var a=r(94752),i=function(e){let t=function(e){let t=s.forwardRef((e,t)=>{let{children:r,...a}=e;if(s.isValidElement(r)){var i;let e,o,l=(i=r,(o=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(o=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),d=function(e,t){let r={...t};for(let s in t){let n=e[s],a=t[s];/^on[A-Z]/.test(s)?n&&a?r[s]=(...e)=>{let t=a(...e);return n(...e),t}:n&&(r[s]=n):"style"===s?r[s]={...n,...a}:"className"===s&&(r[s]=[n,a].filter(Boolean).join(" "))}return{...e,...r}}(a,r.props);return r.type!==s.Fragment&&(d.ref=t?function(...e){return t=>{let r=!1,s=e.map(e=>{let s=n(e,t);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let t=0;t<s.length;t++){let r=s[t];"function"==typeof r?r():n(e[t],null)}}}}(t,l):l),s.cloneElement(r,d)}return s.Children.count(r)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=s.forwardRef((e,r)=>{let{children:n,...i}=e,o=s.Children.toArray(n),d=o.find(l);if(d){let e=d.props.children,n=o.map(t=>t!==d?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...i,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,n):null})}return(0,a.jsx)(t,{...i,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}("Slot"),o=Symbol("radix.slottable");function l(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},31421:e=>{"use strict";e.exports=require("node:child_process")},31870:(e,t,r)=>{"use strict";r.d(t,{SettingsForm:()=>e4});var s=r(99730),n=r(74938),a=r(99752),i=r(57752),o=r(56750),l=i.forwardRef((e,t)=>(0,s.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var d=r(83590);function c({className:e,...t}){return(0,s.jsx)(l,{"data-slot":"label",className:(0,d.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}var u=r(46769),p=r(46854),m=r(1493),h=r(58785),x=r(89768),f=r(93147),v="Switch",[g,b]=(0,m.A)(v),[y,j]=g(v),w=i.forwardRef((e,t)=>{let{__scopeSwitch:r,name:n,checked:a,defaultChecked:l,required:d,disabled:c,value:m="on",onCheckedChange:x,form:f,...g}=e,[b,j]=i.useState(null),w=(0,p.s)(t,e=>j(e)),N=i.useRef(!1),C=!b||f||!!b.closest("form"),[P,R]=(0,h.i)({prop:a,defaultProp:l??!1,onChange:x,caller:v});return(0,s.jsxs)(y,{scope:r,checked:P,disabled:c,children:[(0,s.jsx)(o.sG.button,{type:"button",role:"switch","aria-checked":P,"aria-required":d,"data-state":k(P),"data-disabled":c?"":void 0,disabled:c,value:m,...g,ref:w,onClick:(0,u.m)(e.onClick,e=>{R(e=>!e),C&&(N.current=e.isPropagationStopped(),N.current||e.stopPropagation())})}),C&&(0,s.jsx)(S,{control:b,bubbles:!N.current,name:n,value:m,checked:P,required:d,disabled:c,form:f,style:{transform:"translateX(-100%)"}})]})});w.displayName=v;var N="SwitchThumb",C=i.forwardRef((e,t)=>{let{__scopeSwitch:r,...n}=e,a=j(N,r);return(0,s.jsx)(o.sG.span,{"data-state":k(a.checked),"data-disabled":a.disabled?"":void 0,...n,ref:t})});C.displayName=N;var S=i.forwardRef(({__scopeSwitch:e,control:t,checked:r,bubbles:n=!0,...a},o)=>{let l=i.useRef(null),d=(0,p.s)(l,o),c=(0,x.Z)(r),u=(0,f.X)(t);return i.useEffect(()=>{let e=l.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(c!==r&&t){let s=new Event("click",{bubbles:n});t.call(e,r),e.dispatchEvent(s)}},[c,r,n]),(0,s.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...a,tabIndex:-1,ref:d,style:{...a.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function k(e){return e?"checked":"unchecked"}function P({className:e,...t}){return(0,s.jsx)(w,{"data-slot":"switch",className:(0,d.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,s.jsx)(C,{"data-slot":"switch-thumb",className:(0,d.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}function R({className:e,...t}){return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,d.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}S.displayName="SwitchBubbleInput";var E=r(6754);function T(e,[t,r]){return Math.min(r,Math.max(t,e))}var _=r(46691),q=r(44804),D=r(97727),A=r(55105),I=r(35282),M=r(42703),L=r(32370),B=r(44082),F=r(58576),z=r(18526),O=r(62676),U=r(95200),G=r(34118),V=r(17572),H=[" ","Enter","ArrowUp","ArrowDown"],W=[" ","Enter"],Z="Select",[$,K,X]=(0,_.N)(Z),[J,Y]=(0,m.A)(Z,[X,L.Bk]),Q=(0,L.Bk)(),[ee,et]=J(Z),[er,es]=J(Z),en=e=>{let{__scopeSelect:t,children:r,open:n,defaultOpen:a,onOpenChange:o,value:l,defaultValue:d,onValueChange:c,dir:u,name:p,autoComplete:m,disabled:x,required:f,form:v}=e,g=Q(t),[b,y]=i.useState(null),[j,w]=i.useState(null),[N,C]=i.useState(!1),S=(0,q.jH)(u),[k,P]=(0,h.i)({prop:n,defaultProp:a??!1,onChange:o,caller:Z}),[R,E]=(0,h.i)({prop:l,defaultProp:d,onChange:c,caller:Z}),T=i.useRef(null),_=!b||v||!!b.closest("form"),[D,A]=i.useState(new Set),I=Array.from(D).map(e=>e.props.value).join(";");return(0,s.jsx)(L.bL,{...g,children:(0,s.jsxs)(ee,{required:f,scope:t,trigger:b,onTriggerChange:y,valueNode:j,onValueNodeChange:w,valueNodeHasChildren:N,onValueNodeHasChildrenChange:C,contentId:(0,M.B)(),value:R,onValueChange:E,open:k,onOpenChange:P,dir:S,triggerPointerDownPosRef:T,disabled:x,children:[(0,s.jsx)($.Provider,{scope:t,children:(0,s.jsx)(er,{scope:e.__scopeSelect,onNativeOptionAdd:i.useCallback(e=>{A(t=>new Set(t).add(e))},[]),onNativeOptionRemove:i.useCallback(e=>{A(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),_?(0,s.jsxs)(eO,{"aria-hidden":!0,required:f,tabIndex:-1,name:p,autoComplete:m,value:R,onChange:e=>E(e.target.value),disabled:x,form:v,children:[void 0===R?(0,s.jsx)("option",{value:""}):null,Array.from(D)]},I):null]})})};en.displayName=Z;var ea="SelectTrigger",ei=i.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:n=!1,...a}=e,l=Q(r),d=et(ea,r),c=d.disabled||n,m=(0,p.s)(t,d.onTriggerChange),h=K(r),x=i.useRef("touch"),[f,v,g]=eG(e=>{let t=h().filter(e=>!e.disabled),r=t.find(e=>e.value===d.value),s=eV(t,e,r);void 0!==s&&d.onValueChange(s.value)}),b=e=>{c||(d.onOpenChange(!0),g()),e&&(d.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,s.jsx)(L.Mz,{asChild:!0,...l,children:(0,s.jsx)(o.sG.button,{type:"button",role:"combobox","aria-controls":d.contentId,"aria-expanded":d.open,"aria-required":d.required,"aria-autocomplete":"none",dir:d.dir,"data-state":d.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":eU(d.value)?"":void 0,...a,ref:m,onClick:(0,u.m)(a.onClick,e=>{e.currentTarget.focus(),"mouse"!==x.current&&b(e)}),onPointerDown:(0,u.m)(a.onPointerDown,e=>{x.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(b(e),e.preventDefault())}),onKeyDown:(0,u.m)(a.onKeyDown,e=>{let t=""!==f.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||v(e.key),(!t||" "!==e.key)&&H.includes(e.key)&&(b(),e.preventDefault())})})})});ei.displayName=ea;var eo="SelectValue",el=i.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:a,children:i,placeholder:l="",...d}=e,c=et(eo,r),{onValueNodeHasChildrenChange:u}=c,m=void 0!==i,h=(0,p.s)(t,c.onValueNodeChange);return(0,O.N)(()=>{u(m)},[u,m]),(0,s.jsx)(o.sG.span,{...d,ref:h,style:{pointerEvents:"none"},children:eU(c.value)?(0,s.jsx)(s.Fragment,{children:l}):i})});el.displayName=eo;var ed=i.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...a}=e;return(0,s.jsx)(o.sG.span,{"aria-hidden":!0,...a,ref:t,children:n||"▼"})});ed.displayName="SelectIcon";var ec=e=>(0,s.jsx)(B.Z,{asChild:!0,...e});ec.displayName="SelectPortal";var eu="SelectContent",ep=i.forwardRef((e,t)=>{let r=et(eu,e.__scopeSelect),[n,a]=i.useState();return((0,O.N)(()=>{a(new DocumentFragment)},[]),r.open)?(0,s.jsx)(ef,{...e,ref:t}):n?E.createPortal((0,s.jsx)(em,{scope:e.__scopeSelect,children:(0,s.jsx)($.Slot,{scope:e.__scopeSelect,children:(0,s.jsx)("div",{children:e.children})})}),n):null});ep.displayName=eu;var[em,eh]=J(eu),ex=(0,F.TL)("SelectContent.RemoveScroll"),ef=i.forwardRef((e,t)=>{let{__scopeSelect:r,position:n="item-aligned",onCloseAutoFocus:a,onEscapeKeyDown:o,onPointerDownOutside:l,side:d,sideOffset:c,align:m,alignOffset:h,arrowPadding:x,collisionBoundary:f,collisionPadding:v,sticky:g,hideWhenDetached:b,avoidCollisions:y,...j}=e,w=et(eu,r),[N,C]=i.useState(null),[S,k]=i.useState(null),P=(0,p.s)(t,e=>C(e)),[R,E]=i.useState(null),[T,_]=i.useState(null),q=K(r),[M,L]=i.useState(!1),B=i.useRef(!1);i.useEffect(()=>{if(N)return(0,G.Eq)(N)},[N]),(0,A.Oh)();let F=i.useCallback(e=>{let[t,...r]=q().map(e=>e.ref.current),[s]=r.slice(-1),n=document.activeElement;for(let r of e)if(r===n||(r?.scrollIntoView({block:"nearest"}),r===t&&S&&(S.scrollTop=0),r===s&&S&&(S.scrollTop=S.scrollHeight),r?.focus(),document.activeElement!==n))return},[q,S]),z=i.useCallback(()=>F([R,N]),[F,R,N]);i.useEffect(()=>{M&&z()},[M,z]);let{onOpenChange:O,triggerPointerDownPosRef:U}=w;i.useEffect(()=>{if(N){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(U.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(U.current?.y??0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():N.contains(r.target)||O(!1),document.removeEventListener("pointermove",t),U.current=null};return null!==U.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[N,O,U]),i.useEffect(()=>{let e=()=>O(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[O]);let[H,W]=eG(e=>{let t=q().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),s=eV(t,e,r);s&&setTimeout(()=>s.ref.current.focus())}),Z=i.useCallback((e,t,r)=>{let s=!B.current&&!r;(void 0!==w.value&&w.value===t||s)&&(E(e),s&&(B.current=!0))},[w.value]),$=i.useCallback(()=>N?.focus(),[N]),X=i.useCallback((e,t,r)=>{let s=!B.current&&!r;(void 0!==w.value&&w.value===t||s)&&_(e)},[w.value]),J="popper"===n?eg:ev,Y=J===eg?{side:d,sideOffset:c,align:m,alignOffset:h,arrowPadding:x,collisionBoundary:f,collisionPadding:v,sticky:g,hideWhenDetached:b,avoidCollisions:y}:{};return(0,s.jsx)(em,{scope:r,content:N,viewport:S,onViewportChange:k,itemRefCallback:Z,selectedItem:R,onItemLeave:$,itemTextRefCallback:X,focusSelectedItem:z,selectedItemText:T,position:n,isPositioned:M,searchRef:H,children:(0,s.jsx)(V.A,{as:ex,allowPinchZoom:!0,children:(0,s.jsx)(I.n,{asChild:!0,trapped:w.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,u.m)(a,e=>{w.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,s.jsx)(D.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:o,onPointerDownOutside:l,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>w.onOpenChange(!1),children:(0,s.jsx)(J,{role:"listbox",id:w.contentId,"data-state":w.open?"open":"closed",dir:w.dir,onContextMenu:e=>e.preventDefault(),...j,...Y,onPlaced:()=>L(!0),ref:P,style:{display:"flex",flexDirection:"column",outline:"none",...j.style},onKeyDown:(0,u.m)(j.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||W(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=q().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,s=t.indexOf(r);t=t.slice(s+1)}setTimeout(()=>F(t)),e.preventDefault()}})})})})})})});ef.displayName="SelectContentImpl";var ev=i.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:n,...a}=e,l=et(eu,r),d=eh(eu,r),[c,u]=i.useState(null),[m,h]=i.useState(null),x=(0,p.s)(t,e=>h(e)),f=K(r),v=i.useRef(!1),g=i.useRef(!0),{viewport:b,selectedItem:y,selectedItemText:j,focusSelectedItem:w}=d,N=i.useCallback(()=>{if(l.trigger&&l.valueNode&&c&&m&&b&&y&&j){let e=l.trigger.getBoundingClientRect(),t=m.getBoundingClientRect(),r=l.valueNode.getBoundingClientRect(),s=j.getBoundingClientRect();if("rtl"!==l.dir){let n=s.left-t.left,a=r.left-n,i=e.left-a,o=e.width+i,l=Math.max(o,t.width),d=T(a,[10,Math.max(10,window.innerWidth-10-l)]);c.style.minWidth=o+"px",c.style.left=d+"px"}else{let n=t.right-s.right,a=window.innerWidth-r.right-n,i=window.innerWidth-e.right-a,o=e.width+i,l=Math.max(o,t.width),d=T(a,[10,Math.max(10,window.innerWidth-10-l)]);c.style.minWidth=o+"px",c.style.right=d+"px"}let a=f(),i=window.innerHeight-20,o=b.scrollHeight,d=window.getComputedStyle(m),u=parseInt(d.borderTopWidth,10),p=parseInt(d.paddingTop,10),h=parseInt(d.borderBottomWidth,10),x=u+p+o+parseInt(d.paddingBottom,10)+h,g=Math.min(5*y.offsetHeight,x),w=window.getComputedStyle(b),N=parseInt(w.paddingTop,10),C=parseInt(w.paddingBottom,10),S=e.top+e.height/2-10,k=y.offsetHeight/2,P=u+p+(y.offsetTop+k);if(P<=S){let e=a.length>0&&y===a[a.length-1].ref.current;c.style.bottom="0px";let t=Math.max(i-S,k+(e?C:0)+(m.clientHeight-b.offsetTop-b.offsetHeight)+h);c.style.height=P+t+"px"}else{let e=a.length>0&&y===a[0].ref.current;c.style.top="0px";let t=Math.max(S,u+b.offsetTop+(e?N:0)+k);c.style.height=t+(x-P)+"px",b.scrollTop=P-S+b.offsetTop}c.style.margin="10px 0",c.style.minHeight=g+"px",c.style.maxHeight=i+"px",n?.(),requestAnimationFrame(()=>v.current=!0)}},[f,l.trigger,l.valueNode,c,m,b,y,j,l.dir,n]);(0,O.N)(()=>N(),[N]);let[C,S]=i.useState();(0,O.N)(()=>{m&&S(window.getComputedStyle(m).zIndex)},[m]);let k=i.useCallback(e=>{e&&!0===g.current&&(N(),w?.(),g.current=!1)},[N,w]);return(0,s.jsx)(eb,{scope:r,contentWrapper:c,shouldExpandOnScrollRef:v,onScrollButtonChange:k,children:(0,s.jsx)("div",{ref:u,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:C},children:(0,s.jsx)(o.sG.div,{...a,ref:x,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});ev.displayName="SelectItemAlignedPosition";var eg=i.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:a=10,...i}=e,o=Q(r);return(0,s.jsx)(L.UC,{...o,...i,ref:t,align:n,collisionPadding:a,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});eg.displayName="SelectPopperPosition";var[eb,ey]=J(eu,{}),ej="SelectViewport",ew=i.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:n,...a}=e,l=eh(ej,r),d=ey(ej,r),c=(0,p.s)(t,l.onViewportChange),m=i.useRef(0);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:n}),(0,s.jsx)($.Slot,{scope:r,children:(0,s.jsx)(o.sG.div,{"data-radix-select-viewport":"",role:"presentation",...a,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...a.style},onScroll:(0,u.m)(a.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:s}=d;if(s?.current&&r){let e=Math.abs(m.current-t.scrollTop);if(e>0){let s=window.innerHeight-20,n=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(n<s){let a=n+e,i=Math.min(s,a),o=a-i;r.style.height=i+"px","0px"===r.style.bottom&&(t.scrollTop=o>0?o:0,r.style.justifyContent="flex-end")}}}m.current=t.scrollTop})})})]})});ew.displayName=ej;var eN="SelectGroup",[eC,eS]=J(eN);i.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,a=(0,M.B)();return(0,s.jsx)(eC,{scope:r,id:a,children:(0,s.jsx)(o.sG.div,{role:"group","aria-labelledby":a,...n,ref:t})})}).displayName=eN;var ek="SelectLabel";i.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,a=eS(ek,r);return(0,s.jsx)(o.sG.div,{id:a.id,...n,ref:t})}).displayName=ek;var eP="SelectItem",[eR,eE]=J(eP),eT=i.forwardRef((e,t)=>{let{__scopeSelect:r,value:n,disabled:a=!1,textValue:l,...d}=e,c=et(eP,r),m=eh(eP,r),h=c.value===n,[x,f]=i.useState(l??""),[v,g]=i.useState(!1),b=(0,p.s)(t,e=>m.itemRefCallback?.(e,n,a)),y=(0,M.B)(),j=i.useRef("touch"),w=()=>{a||(c.onValueChange(n),c.onOpenChange(!1))};if(""===n)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,s.jsx)(eR,{scope:r,value:n,disabled:a,textId:y,isSelected:h,onItemTextChange:i.useCallback(e=>{f(t=>t||(e?.textContent??"").trim())},[]),children:(0,s.jsx)($.ItemSlot,{scope:r,value:n,disabled:a,textValue:x,children:(0,s.jsx)(o.sG.div,{role:"option","aria-labelledby":y,"data-highlighted":v?"":void 0,"aria-selected":h&&v,"data-state":h?"checked":"unchecked","aria-disabled":a||void 0,"data-disabled":a?"":void 0,tabIndex:a?void 0:-1,...d,ref:b,onFocus:(0,u.m)(d.onFocus,()=>g(!0)),onBlur:(0,u.m)(d.onBlur,()=>g(!1)),onClick:(0,u.m)(d.onClick,()=>{"mouse"!==j.current&&w()}),onPointerUp:(0,u.m)(d.onPointerUp,()=>{"mouse"===j.current&&w()}),onPointerDown:(0,u.m)(d.onPointerDown,e=>{j.current=e.pointerType}),onPointerMove:(0,u.m)(d.onPointerMove,e=>{j.current=e.pointerType,a?m.onItemLeave?.():"mouse"===j.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,u.m)(d.onPointerLeave,e=>{e.currentTarget===document.activeElement&&m.onItemLeave?.()}),onKeyDown:(0,u.m)(d.onKeyDown,e=>{(m.searchRef?.current===""||" "!==e.key)&&(W.includes(e.key)&&w()," "===e.key&&e.preventDefault())})})})})});eT.displayName=eP;var e_="SelectItemText",eq=i.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:a,...l}=e,d=et(e_,r),c=eh(e_,r),u=eE(e_,r),m=es(e_,r),[h,x]=i.useState(null),f=(0,p.s)(t,e=>x(e),u.onItemTextChange,e=>c.itemTextRefCallback?.(e,u.value,u.disabled)),v=h?.textContent,g=i.useMemo(()=>(0,s.jsx)("option",{value:u.value,disabled:u.disabled,children:v},u.value),[u.disabled,u.value,v]),{onNativeOptionAdd:b,onNativeOptionRemove:y}=m;return(0,O.N)(()=>(b(g),()=>y(g)),[b,y,g]),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.sG.span,{id:u.textId,...l,ref:f}),u.isSelected&&d.valueNode&&!d.valueNodeHasChildren?E.createPortal(l.children,d.valueNode):null]})});eq.displayName=e_;var eD="SelectItemIndicator",eA=i.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return eE(eD,r).isSelected?(0,s.jsx)(o.sG.span,{"aria-hidden":!0,...n,ref:t}):null});eA.displayName=eD;var eI="SelectScrollUpButton",eM=i.forwardRef((e,t)=>{let r=eh(eI,e.__scopeSelect),n=ey(eI,e.__scopeSelect),[a,o]=i.useState(!1),l=(0,p.s)(t,n.onScrollButtonChange);return(0,O.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){o(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),a?(0,s.jsx)(eF,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});eM.displayName=eI;var eL="SelectScrollDownButton",eB=i.forwardRef((e,t)=>{let r=eh(eL,e.__scopeSelect),n=ey(eL,e.__scopeSelect),[a,o]=i.useState(!1),l=(0,p.s)(t,n.onScrollButtonChange);return(0,O.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;o(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),a?(0,s.jsx)(eF,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eB.displayName=eL;var eF=i.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:n,...a}=e,l=eh("SelectScrollButton",r),d=i.useRef(null),c=K(r),p=i.useCallback(()=>{null!==d.current&&(window.clearInterval(d.current),d.current=null)},[]);return i.useEffect(()=>()=>p(),[p]),(0,O.N)(()=>{let e=c().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[c]),(0,s.jsx)(o.sG.div,{"aria-hidden":!0,...a,ref:t,style:{flexShrink:0,...a.style},onPointerDown:(0,u.m)(a.onPointerDown,()=>{null===d.current&&(d.current=window.setInterval(n,50))}),onPointerMove:(0,u.m)(a.onPointerMove,()=>{l.onItemLeave?.(),null===d.current&&(d.current=window.setInterval(n,50))}),onPointerLeave:(0,u.m)(a.onPointerLeave,()=>{p()})})});i.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,s.jsx)(o.sG.div,{"aria-hidden":!0,...n,ref:t})}).displayName="SelectSeparator";var ez="SelectArrow";i.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,a=Q(r),i=et(ez,r),o=eh(ez,r);return i.open&&"popper"===o.position?(0,s.jsx)(L.i3,{...a,...n,ref:t}):null}).displayName=ez;var eO=i.forwardRef(({__scopeSelect:e,value:t,...r},n)=>{let a=i.useRef(null),l=(0,p.s)(n,a),d=(0,x.Z)(t);return i.useEffect(()=>{let e=a.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(d!==t&&r){let s=new Event("change",{bubbles:!0});r.call(e,t),e.dispatchEvent(s)}},[d,t]),(0,s.jsx)(o.sG.select,{...r,style:{...U.Qg,...r.style},ref:l,defaultValue:t})});function eU(e){return""===e||void 0===e}function eG(e){let t=(0,z.c)(e),r=i.useRef(""),s=i.useRef(0),n=i.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(s.current),""!==t&&(s.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),a=i.useCallback(()=>{r.current="",window.clearTimeout(s.current)},[]);return i.useEffect(()=>()=>window.clearTimeout(s.current),[]),[r,n,a]}function eV(e,t,r){var s,n;let a=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=r?e.indexOf(r):-1,o=(s=e,n=Math.max(i,0),s.map((e,t)=>s[(n+t)%s.length]));1===a.length&&(o=o.filter(e=>e!==r));let l=o.find(e=>e.textValue.toLowerCase().startsWith(a.toLowerCase()));return l!==r?l:void 0}eO.displayName="SelectBubbleInput";var eH=r(10439),eW=r(44669);let eZ=(0,r(19161).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);function e$({...e}){return(0,s.jsx)(en,{"data-slot":"select",...e})}function eK({...e}){return(0,s.jsx)(el,{"data-slot":"select-value",...e})}function eX({className:e,size:t="default",children:r,...n}){return(0,s.jsxs)(ei,{"data-slot":"select-trigger","data-size":t,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n,children:[r,(0,s.jsx)(ed,{asChild:!0,children:(0,s.jsx)(eH.A,{className:"size-4 opacity-50"})})]})}function eJ({className:e,children:t,position:r="popper",...n}){return(0,s.jsx)(ec,{children:(0,s.jsxs)(ep,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...n,children:[(0,s.jsx)(eQ,{}),(0,s.jsx)(ew,{className:(0,d.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,s.jsx)(e0,{})]})})}function eY({className:e,children:t,...r}){return(0,s.jsxs)(eT,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...r,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(eA,{children:(0,s.jsx)(eW.A,{className:"size-4"})})}),(0,s.jsx)(eq,{children:t})]})}function eQ({className:e,...t}){return(0,s.jsx)(eM,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(eZ,{className:"size-4"})})}function e0({className:e,...t}){return(0,s.jsx)(eB,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(eH.A,{className:"size-4"})})}var e1=r(22683);function e4({userId:e,extensionSettings:t,preferences:r}){let[o,l]=(0,i.useState)({defaultModel:t.defaultModel||"claude-3-sonnet",autoSave:t.autoSave??!0,codeCompletion:t.codeCompletion??!0,maxTokens:t.maxTokens||4e3,temperature:t.temperature||.7,customPrompt:t.customPrompt||"",theme:r.theme||"system",notifications:r.notifications??!0,analytics:r.analytics??!0,autoConnect:r.autoConnect??!0}),[d,u]=(0,i.useState)(!1),p=(e,t)=>{l(r=>({...r,[e]:t}))},m=async()=>{u(!0);try{let e={defaultModel:o.defaultModel,autoSave:o.autoSave,codeCompletion:o.codeCompletion,maxTokens:o.maxTokens,temperature:o.temperature,customPrompt:o.customPrompt},t={theme:o.theme,notifications:o.notifications,analytics:o.analytics,autoConnect:o.autoConnect};(await fetch("/api/extension/settings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({extensionSettings:e,preferences:t})})).ok?e1.toast.success("Settings saved successfully"):e1.toast.error("Failed to save settings")}catch(e){e1.toast.error("Failed to save settings")}finally{u(!1)}};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"font-semibold",children:"AI Model Settings"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c,{htmlFor:"defaultModel",children:"Default Model"}),(0,s.jsxs)(e$,{value:o.defaultModel,onValueChange:e=>p("defaultModel",e),children:[(0,s.jsx)(eX,{children:(0,s.jsx)(eK,{})}),(0,s.jsxs)(eJ,{children:[(0,s.jsx)(eY,{value:"claude-3-sonnet",children:"Claude 3 Sonnet"}),(0,s.jsx)(eY,{value:"claude-3-haiku",children:"Claude 3 Haiku"}),(0,s.jsx)(eY,{value:"gpt-4",children:"GPT-4"}),(0,s.jsx)(eY,{value:"gpt-3.5-turbo",children:"GPT-3.5 Turbo"}),(0,s.jsx)(eY,{value:"gemini-pro",children:"Gemini Pro"})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c,{htmlFor:"maxTokens",children:"Max Tokens"}),(0,s.jsx)(a.p,{id:"maxTokens",type:"number",min:"100",max:"8000",value:o.maxTokens,onChange:e=>p("maxTokens",parseInt(e.target.value))})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c,{htmlFor:"temperature",children:"Temperature"}),(0,s.jsx)(a.p,{id:"temperature",type:"number",min:"0",max:"2",step:"0.1",value:o.temperature,onChange:e=>p("temperature",parseFloat(e.target.value))})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c,{htmlFor:"customPrompt",children:"Custom System Prompt"}),(0,s.jsx)(R,{id:"customPrompt",placeholder:"Enter a custom system prompt for the AI...",value:o.customPrompt,onChange:e=>p("customPrompt",e.target.value),rows:3})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"font-semibold",children:"Extension Behavior"}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"space-y-0.5",children:[(0,s.jsx)(c,{children:"Auto Save"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Automatically save changes to files"})]}),(0,s.jsx)(P,{checked:o.autoSave,onCheckedChange:e=>p("autoSave",e)})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"space-y-0.5",children:[(0,s.jsx)(c,{children:"Code Completion"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Enable AI-powered code completion"})]}),(0,s.jsx)(P,{checked:o.codeCompletion,onCheckedChange:e=>p("codeCompletion",e)})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"font-semibold",children:"User Preferences"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c,{htmlFor:"theme",children:"Theme"}),(0,s.jsxs)(e$,{value:o.theme,onValueChange:e=>p("theme",e),children:[(0,s.jsx)(eX,{children:(0,s.jsx)(eK,{})}),(0,s.jsxs)(eJ,{children:[(0,s.jsx)(eY,{value:"system",children:"System"}),(0,s.jsx)(eY,{value:"light",children:"Light"}),(0,s.jsx)(eY,{value:"dark",children:"Dark"})]})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"space-y-0.5",children:[(0,s.jsx)(c,{children:"Notifications"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Receive notifications about updates and usage"})]}),(0,s.jsx)(P,{checked:o.notifications,onCheckedChange:e=>p("notifications",e)})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"space-y-0.5",children:[(0,s.jsx)(c,{children:"Usage Analytics"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Allow collection of usage data for analytics"})]}),(0,s.jsx)(P,{checked:o.analytics,onCheckedChange:e=>p("analytics",e)})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"space-y-0.5",children:[(0,s.jsx)(c,{children:"Auto Connect"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Automatically connect extension when VS Code starts"})]}),(0,s.jsx)(P,{checked:o.autoConnect,onCheckedChange:e=>p("autoConnect",e)})]})]}),(0,s.jsx)("div",{className:"pt-4",children:(0,s.jsx)(n.$,{onClick:m,disabled:d,className:"w-full",children:d?"Saving...":"Save Settings"})})]})}},33873:e=>{"use strict";e.exports=require("path")},34069:(e,t,r)=>{"use strict";r.d(t,{w:()=>l});var s=r(81121),n=r.n(s);let a="next-forge",i={name:"Vercel",url:"https://vercel.com/"},o=process.env.VERCEL_PROJECT_PRODUCTION_URL,l=({title:e,description:t,image:r,...s})=>{let l=`${e} | ${a}`,d={title:l,description:t,applicationName:a,metadataBase:o?new URL(`https://${o}`):void 0,authors:[i],creator:i.name,formatDetection:{telephone:!1},appleWebApp:{capable:!0,statusBarStyle:"default",title:l},openGraph:{title:l,description:t,type:"website",siteName:a,locale:"en_US"},publisher:"Vercel",twitter:{card:"summary_large_image",creator:"@vercel"}},c=n()(d,s);return r&&c.openGraph&&(c.openGraph.images=[{url:r,width:1200,height:630,alt:e}]),c}},34631:e=>{"use strict";e.exports=require("tls")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},44793:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(24767).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},46954:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>o,Zp:()=>a,aR:()=>i});var s=r(94752);r(23233);var n=r(58559);function a({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...t})}},48161:e=>{"use strict";e.exports=require("node:os")},49499:(e,t,r)=>{let{createProxy:s}=r(20867);e.exports=s("C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f\\node_modules\\next\\dist\\client\\app-dir\\link.js")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},54287:e=>{"use strict";e.exports=require("console")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},59380:(e,t,r)=>{"use strict";r.d(t,{SettingsForm:()=>s});let s=(0,r(6340).registerClientReference)(function(){throw Error("Attempted to call SettingsForm() from the server but SettingsForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\profile\\settings\\components\\settings-form.tsx","SettingsForm")},59988:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"7fa248ee4cee001992d543e3927d536ddea63c121c":()=>s.ai,"7fd10f19b29f8e8b2951e0bb60d9466e540ba24937":()=>s.ot,"7fe80fb1c9bdcbbae5a7da0586440a249dd4fb207a":()=>n.y,"7ffdf714159b7e9cad55b4d3d168d12a9fa0cc1b9f":()=>s.at});var s=r(54841),n=r(44089)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},80481:e=>{"use strict";e.exports=require("node:readline")},80919:(e,t,r)=>{Promise.resolve().then(r.bind(r,31870)),Promise.resolve().then(r.bind(r,86332)),Promise.resolve().then(r.t.bind(r,41265,23)),Promise.resolve().then(r.bind(r,22683))},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},89768:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var s=r(57752);function n(e){let t=s.useRef({value:e,previous:e});return s.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},91645:e=>{"use strict";e.exports=require("net")},93206:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g,metadata:()=>v});var s=r(94752),n=r(37838),a=r(1359),i=r(18815),o=r(24700),l=r(46954),d=r(34069),c=r(62923),u=r(49499),p=r.n(u),m=r(44793),h=r(59380);let x="Settings",f="Manage your preferences and extension settings.",v=(0,d.w)({title:x,description:f}),g=async()=>{let{userId:e}=await (0,n.j)(),t=await (0,a.N)();e&&t||(0,c.redirect)("/sign-in");let r=await i.database.user.findUnique({where:{clerkId:e}});return r||(0,c.notFound)(),(0,s.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)(o.$,{variant:"ghost",size:"sm",asChild:!0,children:(0,s.jsxs)(p(),{href:"/profile",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Back to Profile"]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:x}),(0,s.jsx)("p",{className:"text-muted-foreground",children:f})]})]}),(0,s.jsxs)("div",{className:"grid gap-6 lg:grid-cols-2",children:[(0,s.jsxs)(l.Zp,{children:[(0,s.jsxs)(l.aR,{children:[(0,s.jsx)(l.ZB,{children:"Extension Settings"}),(0,s.jsx)(l.BT,{children:"Configure settings that sync with your VS Code extension"})]}),(0,s.jsx)(l.Wu,{children:(0,s.jsx)(h.SettingsForm,{userId:r.id,extensionSettings:r.extensionSettings||{},preferences:r.preferences||{}})})]}),(0,s.jsxs)(l.Zp,{children:[(0,s.jsxs)(l.aR,{children:[(0,s.jsx)(l.ZB,{children:"Account Settings"}),(0,s.jsx)(l.BT,{children:"Manage your account preferences and subscription"})]}),(0,s.jsxs)(l.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"text-sm font-medium",children:"Email"}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:r.email})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"text-sm font-medium",children:"Name"}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:r.name||"Not set"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"text-sm font-medium",children:"Subscription"}),(0,s.jsxs)("div",{className:"text-sm text-muted-foreground",children:[r.subscriptionTier," (",r.subscriptionStatus,")"]})]}),(0,s.jsx)("div",{className:"pt-4",children:(0,s.jsx)(o.$,{variant:"outline",className:"w-full",children:"Manage Subscription"})})]})]})]}),(0,s.jsxs)(l.Zp,{children:[(0,s.jsxs)(l.aR,{children:[(0,s.jsx)(l.ZB,{children:"Sync Information"}),(0,s.jsx)(l.BT,{children:"How settings synchronization works between the website and extension"})]}),(0,s.jsxs)(l.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h4",{className:"font-semibold",children:"Extension Settings"}),(0,s.jsxs)("ul",{className:"text-sm text-muted-foreground space-y-1",children:[(0,s.jsx)("li",{children:"• AI model preferences"}),(0,s.jsx)("li",{children:"• API key configurations"}),(0,s.jsx)("li",{children:"• Extension behavior settings"}),(0,s.jsx)("li",{children:"• Custom prompts and templates"})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h4",{className:"font-semibold",children:"User Preferences"}),(0,s.jsxs)("ul",{className:"text-sm text-muted-foreground space-y-1",children:[(0,s.jsx)("li",{children:"• Theme and appearance"}),(0,s.jsx)("li",{children:"• Notification settings"}),(0,s.jsx)("li",{children:"• Privacy preferences"}),(0,s.jsx)("li",{children:"• Usage analytics options"})]})]})]}),(0,s.jsxs)("div",{className:"pt-4 border-t",children:[(0,s.jsx)("h4",{className:"font-semibold mb-2",children:"Sync Status"}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:r.lastExtensionSync?(0,s.jsxs)("p",{children:["Last synced: ",new Date(r.lastExtensionSync).toLocaleString()]}):(0,s.jsx)("p",{children:"Extension not connected - settings will sync when connected"})})]})]})]}),(0,s.jsxs)(l.Zp,{className:"border-destructive",children:[(0,s.jsxs)(l.aR,{children:[(0,s.jsx)(l.ZB,{className:"text-destructive",children:"Danger Zone"}),(0,s.jsx)(l.BT,{children:"Irreversible actions that affect your account and data"})]}),(0,s.jsxs)(l.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border border-destructive rounded-lg",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold",children:"Reset Extension Settings"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Clear all extension settings and preferences"})]}),(0,s.jsx)(o.$,{variant:"destructive",size:"sm",children:"Reset Settings"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border border-destructive rounded-lg",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold",children:"Disconnect All Sessions"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Force disconnect all active extension sessions"})]}),(0,s.jsx)(o.$,{variant:"destructive",size:"sm",children:"Disconnect All"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border border-destructive rounded-lg",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold",children:"Delete Account Data"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Permanently delete all usage data and settings"})]}),(0,s.jsx)(o.$,{variant:"destructive",size:"sm",children:"Delete Data"})]})]})]})]})}},94175:e=>{"use strict";e.exports=require("stream/web")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5319,7911,6239,903,7838,5480,3319,277,2644,7914,3051,4841,8004,1121,864,3781,6648],()=>r(26483));module.exports=s})();
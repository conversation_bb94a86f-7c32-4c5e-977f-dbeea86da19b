(()=>{var e={};e.id=7183,e.ids=[7183],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},13440:e=>{"use strict";e.exports=require("util/types")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21117:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.default,__next_app__:()=>d,pages:()=>l,routeModule:()=>u,tree:()=>c});var r=t(57864),n=t(94327),i=t(70814),a=t(17984),o={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>a[e]);t.d(s,o);let c={children:["",{children:["(authenticated)",{children:["profile",{children:["extension",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,75756)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\profile\\extension\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,16703)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,52945,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,56940))).default(e)],apple:[async e=>(await Promise.resolve().then(t.bind(t,54526))).default(e)],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,36334))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,29622)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,70814)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,52945,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,56940))).default(e)],apple:[async e=>(await Promise.resolve().then(t.bind(t,54526))).default(e)],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,36334))).default(e)],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\profile\\extension\\page.tsx"],d={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(authenticated)/profile/extension/page",pathname:"/profile/extension",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},21820:e=>{"use strict";e.exports=require("os")},23979:(e,s,t)=>{Promise.resolve().then(t.bind(t,76426)),Promise.resolve().then(t.bind(t,84158)),Promise.resolve().then(t.t.bind(t,21034,23)),Promise.resolve().then(t.t.bind(t,49499,23)),Promise.resolve().then(t.bind(t,93665))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},34311:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19161).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},34631:e=>{"use strict";e.exports=require("tls")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},43903:(e,s,t)=>{"use strict";t.d(s,{ApiKeyManager:()=>x});var r=t(99730),n=t(74938),i=t(99752),a=t(57752),o=t(22683),c=t(19161);let l=(0,c.A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),d=(0,c.A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var u=t(60628),p=t(71265);function x({apiKey:e,userId:s}){let[t,c]=(0,a.useState)(!1),[x,h]=(0,a.useState)(!1),m=async()=>{if(e)try{await navigator.clipboard.writeText(e),o.toast.success("API key copied to clipboard")}catch(e){o.toast.error("Failed to copy API key")}},y=async()=>{h(!0);try{(await fetch("/api/extension/generate-key",{method:"POST"})).ok?(o.toast.success("New API key generated successfully"),window.location.reload()):o.toast.error("Failed to generate new API key")}catch(e){o.toast.error("Failed to generate new API key")}finally{h(!1)}},f=e?`${e.slice(0,12)}${"*".repeat(e.length-16)}${e.slice(-4)}`:"";return(0,r.jsx)("div",{className:"space-y-4",children:e?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Your API Key"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(i.p,{type:"text",value:t?e:f,readOnly:!0,className:"font-mono text-sm"}),(0,r.jsx)(n.$,{variant:"outline",size:"icon",onClick:()=>c(!t),children:t?(0,r.jsx)(l,{className:"h-4 w-4"}):(0,r.jsx)(d,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:"outline",size:"icon",onClick:m,children:(0,r.jsx)(u.A,{className:"h-4 w-4"})})]})]}),(0,r.jsx)("div",{className:"flex gap-2",children:(0,r.jsxs)(n.$,{variant:"outline",onClick:y,disabled:x,className:"flex-1",children:[(0,r.jsx)(p.A,{className:`h-4 w-4 mr-2 ${x?"animate-spin":""}`}),x?"Generating...":"Regenerate Key"]})}),(0,r.jsxs)("div",{className:"text-xs text-muted-foreground space-y-1",children:[(0,r.jsx)("p",{children:"• Keep your API key secure and don't share it with others"}),(0,r.jsx)("p",{children:"• Regenerating will invalidate the current key"}),(0,r.jsx)("p",{children:"• The extension will need to reconnect after regenerating"})]})]}):(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"No API key generated yet. Connect your extension to generate one automatically."}),(0,r.jsxs)(n.$,{onClick:y,disabled:x,className:"w-full",children:[(0,r.jsx)(p.A,{className:`h-4 w-4 mr-2 ${x?"animate-spin":""}`}),x?"Generating...":"Generate API Key"]})]})})}},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},54287:e=>{"use strict";e.exports=require("console")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},62067:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19161).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71265:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19161).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},73024:e=>{"use strict";e.exports=require("node:fs")},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75756:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>w,metadata:()=>b});var r=t(94752),n=t(37838),i=t(1359),a=t(18815),o=t(24700),c=t(46954),l=t(70483),d=t(34069),u=t(62923),p=t(49499),x=t.n(p),h=t(44793),m=t(43131);let y=(0,t(24767).A)("unplug",[["path",{d:"m19 5 3-3",key:"yk6iyv"}],["path",{d:"m2 22 3-3",key:"19mgm9"}],["path",{d:"M6.3 20.3a2.4 2.4 0 0 0 3.4 0L12 18l-6-6-2.3 2.3a2.4 2.4 0 0 0 0 3.4Z",key:"goz73y"}],["path",{d:"M7.5 13.5 10 11",key:"7xgeeb"}],["path",{d:"M10.5 16.5 13 14",key:"10btkg"}],["path",{d:"m12 6 6 6 2.3-2.3a2.4 2.4 0 0 0 0-3.4l-2.6-2.6a2.4 2.4 0 0 0-3.4 0Z",key:"1snsnr"}]]);var f=t(84158),v=t(76426);let j="Extension Management",g="Manage your VS Code extension connection and settings.",b=(0,d.w)({title:j,description:g}),w=async()=>{let{userId:e}=await (0,n.j)(),s=await (0,i.N)();e&&s||(0,u.redirect)("/sign-in");let t=await a.database.user.findUnique({where:{clerkId:e},include:{extensionSessions:{orderBy:{lastActiveAt:"desc"}}}});t||(0,u.notFound)();let d=t.extensionSessions.filter(e=>e.isActive),p=t.extensionSessions.filter(e=>!e.isActive);return(0,r.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(o.$,{variant:"ghost",size:"sm",asChild:!0,children:(0,r.jsxs)(x(),{href:"/profile",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Back to Profile"]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:j}),(0,r.jsx)("p",{className:"text-muted-foreground",children:g})]})]}),(0,r.jsxs)("div",{className:"grid gap-6 lg:grid-cols-2",children:[(0,r.jsxs)(c.Zp,{children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsx)(c.ZB,{children:"Connection Status"}),(0,r.jsx)(c.BT,{children:"Current status of your VS Code extension connection"})]}),(0,r.jsxs)(c.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"font-medium",children:"Extension Status:"}),(0,r.jsx)(l.E,{variant:d.length>0?"default":"secondary",children:d.length>0?"Connected":"Disconnected"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"font-medium",children:"Active Sessions:"}),(0,r.jsx)("span",{children:d.length})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"font-medium",children:"Terms Accepted:"}),(0,r.jsx)(l.E,{variant:t.termsAccepted?"default":"destructive",children:t.termsAccepted?"Yes":"No"})]}),t.lastExtensionSync&&(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"font-medium",children:"Last Sync:"}),(0,r.jsx)("span",{className:"text-sm",children:new Date(t.lastExtensionSync).toLocaleString()})]}),(0,r.jsx)("div",{className:"pt-4 space-y-2",children:t.termsAccepted?0===d.length?(0,r.jsx)(o.$,{className:"w-full",children:"Connect Extension"}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)(o.$,{variant:"outline",className:"w-full",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Refresh Connection"]}),(0,r.jsxs)(o.$,{variant:"destructive",className:"w-full",children:[(0,r.jsx)(y,{className:"h-4 w-4 mr-2"}),"Disconnect All Sessions"]})]}):(0,r.jsx)(o.$,{asChild:!0,className:"w-full",children:(0,r.jsx)(x(),{href:"/terms",children:"Accept Terms to Connect"})})})]})]}),(0,r.jsxs)(c.Zp,{children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsx)(c.ZB,{children:"API Key"}),(0,r.jsx)(c.BT,{children:"Manage your extension API key for secure communication"})]}),(0,r.jsx)(c.Wu,{children:(0,r.jsx)(v.ApiKeyManager,{apiKey:t.extensionApiKey,userId:t.id})})]})]}),(0,r.jsxs)(c.Zp,{children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsx)(c.ZB,{children:"Extension Sessions"}),(0,r.jsx)(c.BT,{children:"View and manage your active and past extension sessions"})]}),(0,r.jsx)(c.Wu,{children:(0,r.jsx)(f.ExtensionSessionsList,{activeSessions:d,inactiveSessions:p})})]}),(0,r.jsxs)(c.Zp,{children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsx)(c.ZB,{children:"Connection Instructions"}),(0,r.jsx)(c.BT,{children:"How to connect your VS Code extension to this website"})]}),(0,r.jsxs)(c.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h4",{className:"font-semibold",children:"Automatic Connection (Recommended)"}),(0,r.jsxs)("ol",{className:"list-decimal list-inside space-y-1 text-sm text-muted-foreground",children:[(0,r.jsx)("li",{children:"Make sure you have accepted the terms of service on this website"}),(0,r.jsx)("li",{children:'Click the "Connect Extension" button above'}),(0,r.jsx)("li",{children:"VS Code will open automatically and prompt you to connect"}),(0,r.jsx)("li",{children:"Follow the prompts in VS Code to complete the connection"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h4",{className:"font-semibold",children:"Manual Connection"}),(0,r.jsxs)("ol",{className:"list-decimal list-inside space-y-1 text-sm text-muted-foreground",children:[(0,r.jsx)("li",{children:"Open VS Code with the Cubent extension installed"}),(0,r.jsx)("li",{children:"Open the Command Palette (Ctrl+Shift+P / Cmd+Shift+P)"}),(0,r.jsx)("li",{children:'Run the command "Cubent: Connect to Website"'}),(0,r.jsx)("li",{children:"Enter this website URL when prompted"}),(0,r.jsx)("li",{children:"Complete the authentication flow"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h4",{className:"font-semibold",children:"Troubleshooting"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-sm text-muted-foreground",children:[(0,r.jsx)("li",{children:"Make sure you're signed in to the same account in both VS Code and this website"}),(0,r.jsx)("li",{children:"Check that the Cubent extension is installed and up to date"}),(0,r.jsx)("li",{children:"Try refreshing the connection if it appears stuck"}),(0,r.jsx)("li",{children:"Contact support if you continue to experience issues"})]})]})]})]})]})}},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76426:(e,s,t)=>{"use strict";t.d(s,{ApiKeyManager:()=>r});let r=(0,t(6340).registerClientReference)(function(){throw Error("Attempted to call ApiKeyManager() from the server but ApiKeyManager is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\profile\\extension\\components\\api-key-manager.tsx","ApiKeyManager")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79444:(e,s,t)=>{"use strict";t.d(s,{ExtensionSessionsList:()=>h});var r=t(99730),n=t(74938),i=t(87785),a=t(85733),o=t(57752),c=t(22683),l=t(62067),d=t(19161);let u=(0,d.A)("unplug",[["path",{d:"m19 5 3-3",key:"yk6iyv"}],["path",{d:"m2 22 3-3",key:"19mgm9"}],["path",{d:"M6.3 20.3a2.4 2.4 0 0 0 3.4 0L12 18l-6-6-2.3 2.3a2.4 2.4 0 0 0 0 3.4Z",key:"goz73y"}],["path",{d:"M7.5 13.5 10 11",key:"7xgeeb"}],["path",{d:"M10.5 16.5 13 14",key:"10btkg"}],["path",{d:"m12 6 6 6 2.3-2.3a2.4 2.4 0 0 0 0-3.4l-2.6-2.6a2.4 2.4 0 0 0-3.4 0Z",key:"1snsnr"}]]),p=(0,d.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var x=t(34311);function h({activeSessions:e,inactiveSessions:s}){let[t,d]=(0,o.useState)(null),h=async e=>{d(e);try{(await fetch(`/api/extension/sessions?sessionId=${e}`,{method:"DELETE"})).ok?(c.toast.success("Session disconnected successfully"),window.location.reload()):c.toast.error("Failed to disconnect session")}catch(e){c.toast.error("Failed to disconnect session")}finally{d(null)}},m=e=>new Date(e).toLocaleString(),y=e=>{let s=new Date().getTime()-new Date(e).getTime(),t=Math.floor(s/6e4),r=Math.floor(s/36e5),n=Math.floor(s/864e5);return t<1?"Just now":t<60?`${t}m ago`:r<24?`${r}h ago`:`${n}d ago`};return 0===e.length&&0===s.length?(0,r.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,r.jsx)("p",{children:"No extension sessions found."}),(0,r.jsx)("p",{className:"text-sm",children:"Connect your VS Code extension to see sessions here."})]}):(0,r.jsxs)("div",{className:"space-y-6",children:[e.length>0&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(l.A,{className:"h-4 w-4 text-green-500"}),(0,r.jsxs)("h3",{className:"font-semibold",children:["Active Sessions (",e.length,")"]})]}),(0,r.jsx)("div",{className:"space-y-3",children:e.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(i.E,{variant:"default",children:"Active"}),(0,r.jsxs)("span",{className:"font-mono text-sm",children:[e.sessionId.slice(0,8),"..."]})]}),(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)("span",{children:["Connected: ",m(e.createdAt)]}),(0,r.jsxs)("span",{children:["Last seen: ",y(e.lastActiveAt)]})]})})]}),(0,r.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>h(e.sessionId),disabled:t===e.sessionId,children:[(0,r.jsx)(u,{className:"h-4 w-4 mr-2"}),t===e.sessionId?"Disconnecting...":"Disconnect"]})]},e.id))})]}),e.length>0&&s.length>0&&(0,r.jsx)(a.Separator,{}),s.length>0&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(p,{className:"h-4 w-4 text-gray-500"}),(0,r.jsxs)("h3",{className:"font-semibold",children:["Past Sessions (",s.length,")"]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[s.slice(0,5).map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg opacity-60",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(i.E,{variant:"secondary",children:"Inactive"}),(0,r.jsxs)("span",{className:"font-mono text-sm",children:[e.sessionId.slice(0,8),"..."]})]}),(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)("span",{children:["Connected: ",m(e.createdAt)]}),(0,r.jsxs)("span",{children:["Last seen: ",m(e.lastActiveAt)]})]})})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-muted-foreground",children:[(0,r.jsx)(x.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"text-sm",children:"Disconnected"})]})]},e.id)),s.length>5&&(0,r.jsxs)("p",{className:"text-sm text-muted-foreground text-center",children:["... and ",s.length-5," more past sessions"]})]})]})]})}},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},80481:e=>{"use strict";e.exports=require("node:readline")},81630:e=>{"use strict";e.exports=require("http")},83651:(e,s,t)=>{Promise.resolve().then(t.bind(t,43903)),Promise.resolve().then(t.bind(t,79444)),Promise.resolve().then(t.bind(t,86332)),Promise.resolve().then(t.t.bind(t,41265,23)),Promise.resolve().then(t.bind(t,22683))},83997:e=>{"use strict";e.exports=require("tty")},84158:(e,s,t)=>{"use strict";t.d(s,{ExtensionSessionsList:()=>r});let r=(0,t(6340).registerClientReference)(function(){throw Error("Attempted to call ExtensionSessionsList() from the server but ExtensionSessionsList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\profile\\extension\\components\\extension-sessions-list.tsx","ExtensionSessionsList")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},87785:(e,s,t)=>{"use strict";t.d(s,{E:()=>c});var r=t(99730);t(57752);var n=t(58576),i=t(72795),a=t(83590);let o=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c({className:e,variant:s,asChild:t=!1,...i}){let c=t?n.DX:"span";return(0,r.jsx)(c,{"data-slot":"badge",className:(0,a.cn)(o({variant:s}),e),...i})}},91645:e=>{"use strict";e.exports=require("net")},94175:e=>{"use strict";e.exports=require("stream/web")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[5319,7911,6239,903,7838,5480,3319,277,2644,7914,3051,4841,8004,1121,864,3781,6648,5432],()=>t(21117));module.exports=r})();
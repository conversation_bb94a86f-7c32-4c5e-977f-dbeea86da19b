(()=>{var e={};e.id=893,e.ids=[893],e.modules={1359:(e,t,s)=>{"use strict";s.d(t,{N:()=>n});var r=s(12901),i=s(37838);async function n(){s(1447);let{userId:e}=await (0,i.j)();return e?(await (0,r.$)()).users.getUser(e):null}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6725:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>b,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>g});var r={};s.r(r),s.d(r,{GET:()=>d});var i=s(26142),n=s(94327),a=s(34862),o=s(26239),u=s(37838),l=s(1359),c=s(18815);async function d(e){try{let{userId:e}=await (0,u.j)(),t=await (0,l.N)();if(!e||!t)return o.NextResponse.json({error:"Unauthorized"},{status:401});let s=await c.database.user.findUnique({where:{clerkId:e},select:{id:!0,cubentUnitsUsed:!0,cubentUnitsLimit:!0,subscriptionTier:!0}});s||(s=await c.database.user.create({data:{clerkId:e,email:t.emailAddresses[0]?.emailAddress||"",name:`${t.firstName||""} ${t.lastName||""}`.trim()||null,picture:t.imageUrl},select:{id:!0,cubentUnitsUsed:!0,cubentUnitsLimit:!0,subscriptionTier:!0}}));let r=await c.database.usageAnalytics.count({where:{userId:s.id}}),i=new Date,n=new Date(i.getFullYear(),i.getMonth(),1),a=new Date(i.getFullYear(),i.getMonth()+1,0),d=await c.database.usageMetrics.aggregate({where:{userId:s.id,date:{gte:n,lte:a}},_sum:{cubentUnitsUsed:!0,requestsMade:!0}}),p={totalCubentUnits:12.5,totalMessages:45,monthlyUsage:{cubentUnits:8.2,messages:28}},h=s.cubentUnitsUsed&&s.cubentUnitsUsed>0||r>0;return o.NextResponse.json({success:!0,totalCubentUnits:h?s.cubentUnitsUsed||0:p.totalCubentUnits,totalMessages:h?r:p.totalMessages,userLimit:s.cubentUnitsLimit||50,subscriptionTier:s.subscriptionTier||"free_trial",monthlyUsage:{cubentUnits:h?d._sum.cubentUnitsUsed||0:p.monthlyUsage.cubentUnits,messages:h?d._sum.requestsMade||0:p.monthlyUsage.messages},isDemo:!h})}catch(e){return console.error("Error fetching usage stats:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}let p=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/extension/usage/stats/route",pathname:"/api/extension/usage/stats",filename:"route",bundlePath:"app/api/extension/usage/stats/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\usage\\stats\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:h,workUnitAsyncStorage:g,serverHooks:m}=p;function b(){return(0,a.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:g})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},12901:(e,t,s)=>{"use strict";s.d(t,{$:()=>l});var r=s(8741),i=s(23056),n=s(76315),a=s(97495);let o=new(s(16698)).AsyncLocalStorage;var u=s(60606);let l=async()=>{var e,t;let s;try{let e=await (0,i.TG)(),t=(0,a._b)(e,r.AA.Headers.ClerkRequestData);s=(0,u.Kk)(t)}catch(e){if(e&&(0,i.Sz)(e))throw e}let l=null!=(t=null==(e=o.getStore())?void 0:e.get("requestData"))?t:s;return(null==l?void 0:l.secretKey)||(null==l?void 0:l.publishableKey)?(0,n.n)(l):(0,n.n)({})}},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26142:(e,t,s)=>{"use strict";e.exports=s(44870)},26790:(e,t,s)=>{"use strict";s.d(t,{z:()=>E});var r,i,n,a,o,u,l,c,d,p,h,g,m,b,f,y,S,k,w,x=s(45940);s(92867);var v=s(37081);s(27322),s(6264);var q=s(49530),U=s(57136),K=s(94051),M=class{constructor(){(0,K.VK)(this,n),(0,K.VK)(this,r,"clerk_telemetry_throttler"),(0,K.VK)(this,i,864e5)}isEventThrottled(e){if(!(0,K.S7)(this,n,u))return!1;let t=Date.now(),s=(0,K.jq)(this,n,a).call(this,e),l=(0,K.S7)(this,n,o)?.[s];if(!l){let e={...(0,K.S7)(this,n,o),[s]:t};localStorage.setItem((0,K.S7)(this,r),JSON.stringify(e))}if(l&&t-l>(0,K.S7)(this,i)){let e=(0,K.S7)(this,n,o);delete e[s],localStorage.setItem((0,K.S7)(this,r),JSON.stringify(e))}return!!l}};r=new WeakMap,i=new WeakMap,n=new WeakSet,a=function(e){let{sk:t,pk:s,payload:r,...i}=e,n={...r,...i};return JSON.stringify(Object.keys({...r,...i}).sort().map(e=>n[e]))},o=function(){let e=localStorage.getItem((0,K.S7)(this,r));return e?JSON.parse(e):{}},u=function(){if("undefined"==typeof window)return!1;let e=window.localStorage;if(!e)return!1;try{let t="test";return e.setItem(t,t),e.removeItem(t),!0}catch(t){return t instanceof DOMException&&("QuotaExceededError"===t.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.name)&&e.length>0&&e.removeItem((0,K.S7)(this,r)),!1}};var R={samplingRate:1,maxBufferSize:5,endpoint:"https://clerk-telemetry.com"},j=class{constructor(e){(0,K.VK)(this,g),(0,K.VK)(this,l),(0,K.VK)(this,c),(0,K.VK)(this,d,{}),(0,K.VK)(this,p,[]),(0,K.VK)(this,h),(0,K.OV)(this,l,{maxBufferSize:e.maxBufferSize??R.maxBufferSize,samplingRate:e.samplingRate??R.samplingRate,disabled:e.disabled??!1,debug:e.debug??!1,endpoint:R.endpoint}),e.clerkVersion||"undefined"!=typeof window?(0,K.S7)(this,d).clerkVersion=e.clerkVersion??"":(0,K.S7)(this,d).clerkVersion="",(0,K.S7)(this,d).sdk=e.sdk,(0,K.S7)(this,d).sdkVersion=e.sdkVersion,(0,K.S7)(this,d).publishableKey=e.publishableKey??"";let t=(0,U.q5)(e.publishableKey);t&&((0,K.S7)(this,d).instanceType=t.instanceType),e.secretKey&&((0,K.S7)(this,d).secretKey=e.secretKey.substring(0,16)),(0,K.OV)(this,c,new M)}get isEnabled(){return!("development"!==(0,K.S7)(this,d).instanceType||(0,K.S7)(this,l).disabled||"undefined"!=typeof process&&(0,q.zz)(process.env.CLERK_TELEMETRY_DISABLED)||"undefined"!=typeof window&&window?.navigator?.webdriver)}get isDebug(){return(0,K.S7)(this,l).debug||"undefined"!=typeof process&&(0,q.zz)(process.env.CLERK_TELEMETRY_DEBUG)}record(e){let t=(0,K.jq)(this,g,w).call(this,e.event,e.payload);(0,K.jq)(this,g,S).call(this,t.event,t),(0,K.jq)(this,g,m).call(this,t,e.eventSamplingRate)&&((0,K.S7)(this,p).push(t),(0,K.jq)(this,g,f).call(this))}};l=new WeakMap,c=new WeakMap,d=new WeakMap,p=new WeakMap,h=new WeakMap,g=new WeakSet,m=function(e,t){return this.isEnabled&&!this.isDebug&&(0,K.jq)(this,g,b).call(this,e,t)},b=function(e,t){let s=Math.random();return!!(s<=(0,K.S7)(this,l).samplingRate&&(void 0===t||s<=t))&&!(0,K.S7)(this,c).isEventThrottled(e)},f=function(){if("undefined"==typeof window)return void(0,K.jq)(this,g,y).call(this);if((0,K.S7)(this,p).length>=(0,K.S7)(this,l).maxBufferSize){(0,K.S7)(this,h)&&("undefined"!=typeof cancelIdleCallback?cancelIdleCallback:clearTimeout)((0,K.S7)(this,h)),(0,K.jq)(this,g,y).call(this);return}(0,K.S7)(this,h)||("requestIdleCallback"in window?(0,K.OV)(this,h,requestIdleCallback(()=>{(0,K.jq)(this,g,y).call(this)})):(0,K.OV)(this,h,setTimeout(()=>{(0,K.jq)(this,g,y).call(this)},0)))},y=function(){fetch(new URL("/v1/event",(0,K.S7)(this,l).endpoint),{method:"POST",body:JSON.stringify({events:(0,K.S7)(this,p)}),headers:{"Content-Type":"application/json"}}).catch(()=>void 0).then(()=>{(0,K.OV)(this,p,[])}).catch(()=>void 0)},S=function(e,t){this.isDebug&&(void 0!==console.groupCollapsed?(console.groupCollapsed("[clerk/telemetry]",e),console.log(t),console.groupEnd()):console.log("[clerk/telemetry]",e,t))},k=function(){let e={name:(0,K.S7)(this,d).sdk,version:(0,K.S7)(this,d).sdkVersion};return"undefined"!=typeof window&&window.Clerk&&(e={...e,...window.Clerk.constructor.sdkMetadata}),e},w=function(e,t){let s=(0,K.jq)(this,g,k).call(this);return{event:e,cv:(0,K.S7)(this,d).clerkVersion??"",it:(0,K.S7)(this,d).instanceType??"",sdk:s.name,sdkv:s.version,...(0,K.S7)(this,d).publishableKey?{pk:(0,K.S7)(this,d).publishableKey}:{},...(0,K.S7)(this,d).secretKey?{sk:(0,K.S7)(this,d).secretKey}:{},payload:t}};function E(e){let t={...e},s=(0,x.y3)(t),r=(0,x.Bs)({options:t,apiClient:s}),i=new j({...e.telemetry,publishableKey:t.publishableKey,secretKey:t.secretKey,samplingRate:.1,...t.sdkMetadata?{sdk:t.sdkMetadata.name,sdkVersion:t.sdkMetadata.version}:{}});return{...s,...r,telemetry:i}}(0,v.C)(x.nr)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76315:(e,t,s)=>{"use strict";s.d(t,{n:()=>a});var r=s(26790),i=s(54726);let n={secretKey:i.rB,publishableKey:i.At,apiUrl:i.H$,apiVersion:i.mG,userAgent:"@clerk/nextjs@6.20.0",proxyUrl:i.Rg,domain:i.V2,isSatellite:i.fS,sdkMetadata:i.tm,telemetry:{disabled:i.nN,debug:i.Mh}},a=e=>(0,r.z)({...n,...e})},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},89259:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[5319,7911,6239,903,7838,5480,864],()=>s(6725));module.exports=r})();
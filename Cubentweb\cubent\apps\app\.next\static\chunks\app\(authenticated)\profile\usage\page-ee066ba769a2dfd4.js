(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2689],{31918:(e,t,s)=>{"use strict";s.d(t,{cn:()=>i}),s(63410);var r=s(49973);s(13957);var a=s(22928);let i=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.QP)((0,r.$)(t))}},55257:(e,t,s)=>{"use strict";s.d(t,{UsageTable:()=>u});var r=s(6024),a=s(50628),i=s(70234),n=s(79242),l=s(69761),d=s(45707);let o=(0,d.A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]),c=(0,d.A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);function u(e){let{data:t}=e,[s,d]=(0,a.useState)(1),[u,m]=(0,a.useState)(""),x=e=>e.toFixed(2),h=e=>e.toLocaleString(),v=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),g=t.filter(e=>v(e.date).toLowerCase().includes(u.toLowerCase())),f=Math.ceil(g.length/10),b=(s-1)*10,p=g.slice(b,b+10),j=e=>{d(Math.max(1,Math.min(e,f)))};return 0===t.length?(0,r.jsx)("div",{className:"text-center py-8 text-muted-foreground",children:(0,r.jsx)("p",{children:"No usage data available"})}):(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsxs)("div",{className:"relative flex-1 max-w-sm",children:[(0,r.jsx)(l.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,r.jsx)(n.p,{placeholder:"Search by date...",value:u,onChange:e=>{m(e.target.value),d(1)},className:"pl-10"})]})}),(0,r.jsx)("div",{className:"border rounded-lg overflow-hidden",children:(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{className:"bg-muted/50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"text-left p-4 font-medium",children:"Date"}),(0,r.jsx)("th",{className:"text-right p-4 font-medium",children:"Cubent Units"}),(0,r.jsx)("th",{className:"text-right p-4 font-medium",children:"Messages"}),(0,r.jsx)("th",{className:"text-right p-4 font-medium",children:"Avg Units/Message"})]})}),(0,r.jsx)("tbody",{children:p.map((e,t)=>(0,r.jsxs)("tr",{className:t%2==0?"bg-background":"bg-muted/25",children:[(0,r.jsx)("td",{className:"p-4",children:v(e.date)}),(0,r.jsx)("td",{className:"p-4 text-right font-mono",children:x(e.cubentUnitsUsed||0)}),(0,r.jsx)("td",{className:"p-4 text-right font-mono",children:h(e.requestsMade)}),(0,r.jsx)("td",{className:"p-4 text-right font-mono",children:e.requestsMade>0&&(e.cubentUnitsUsed||0)>0?x((e.cubentUnitsUsed||0)/e.requestsMade):"-"})]},e.id))})]})})}),f>1&&(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Showing ",b+1," to ",Math.min(b+10,g.length)," of ",g.length," entries"]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>j(s-1),disabled:1===s,children:[(0,r.jsx)(o,{className:"h-4 w-4"}),"Previous"]}),(0,r.jsx)("div",{className:"flex items-center space-x-1",children:Array.from({length:Math.min(5,f)},(e,t)=>{let a;return a=f<=5||s<=3?t+1:s>=f-2?f-4+t:s-2+t,(0,r.jsx)(i.$,{variant:s===a?"default":"outline",size:"sm",onClick:()=>j(a),className:"w-8 h-8 p-0",children:a},a)})}),(0,r.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>j(s+1),disabled:s===f,children:["Next",(0,r.jsx)(c,{className:"h-4 w-4"})]})]})]})]})}},67013:(e,t,s)=>{Promise.resolve().then(s.bind(s,71270)),Promise.resolve().then(s.bind(s,55257)),Promise.resolve().then(s.bind(s,43432)),Promise.resolve().then(s.t.bind(s,35685,23)),Promise.resolve().then(s.bind(s,13957))},69761:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(45707).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},70234:(e,t,s)=>{"use strict";s.d(t,{$:()=>d});var r=s(6024);s(50628);var a=s(89840),i=s(81197),n=s(31918);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:s,size:i,asChild:d=!1,...o}=e,c=d?a.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,n.cn)(l({variant:s,size:i,className:t})),...o})}},71270:(e,t,s)=>{"use strict";s.d(t,{UsageChart:()=>i});var r=s(6024),a=s(50628);function i(e){let{data:t}=e,s=(0,a.useMemo)(()=>t.map(e=>({date:new Date(e.date).toLocaleDateString("en-US",{month:"short",day:"numeric"}),cubentUnits:e.cubentUnitsUsed||0,requests:e.requestsMade})),[t]),i=Math.max(...s.map(e=>e.cubentUnits)),n=Math.max(...s.map(e=>e.requests));return 0===s.length?(0,r.jsx)("div",{className:"h-64 flex items-center justify-center text-muted-foreground",children:(0,r.jsx)("p",{children:"No usage data available"})}):(0,r.jsxs)("div",{className:"h-64 w-full",children:[(0,r.jsx)("div",{className:"flex items-end justify-between h-full space-x-1",children:s.map((e,t)=>(0,r.jsxs)("div",{className:"flex flex-col items-center flex-1 h-full",children:[(0,r.jsxs)("div",{className:"flex-1 flex flex-col justify-end w-full space-y-1",children:[(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)("div",{className:"bg-blue-500 rounded-t-sm transition-all hover:bg-blue-600",style:{height:"".concat(i>0?e.cubentUnits/i*100:0,"%"),minHeight:e.cubentUnits>0?"2px":"0px"}}),(0,r.jsxs)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap",children:[e.cubentUnits.toFixed(2)," units"]})]}),(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)("div",{className:"bg-green-500 rounded-t-sm transition-all hover:bg-green-600",style:{height:"".concat(n>0?e.requests/n*80:0,"%"),minHeight:e.requests>0?"2px":"0px"}}),(0,r.jsxs)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap",children:[e.requests," messages"]})]})]}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground mt-2 transform -rotate-45 origin-left",children:e.date})]},t))}),(0,r.jsxs)("div",{className:"flex justify-center space-x-6 mt-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded"}),(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:"Cubent Units"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded"}),(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:"Messages"})]})]})]})}},79242:(e,t,s)=>{"use strict";s.d(t,{p:()=>i});var r=s(6024);s(50628);var a=s(31918);function i(e){let{className:t,type:s,...i}=e;return(0,r.jsx)("input",{type:s,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...i})}},81197:(e,t,s)=>{"use strict";s.d(t,{F:()=>n});var r=s(49973);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=r.$,n=(e,t)=>s=>{var r;if((null==t?void 0:t.variants)==null)return i(e,null==s?void 0:s.class,null==s?void 0:s.className);let{variants:n,defaultVariants:l}=t,d=Object.keys(n).map(e=>{let t=null==s?void 0:s[e],r=null==l?void 0:l[e];if(null===t)return null;let i=a(t)||a(r);return n[e][i]}),o=s&&Object.entries(s).reduce((e,t)=>{let[s,r]=t;return void 0===r||(e[s]=r),e},{});return i(e,d,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:s,className:r,...a}=t;return Object.entries(a).every(e=>{let[t,s]=e;return Array.isArray(s)?s.includes({...l,...o}[t]):({...l,...o})[t]===s})?[...e,s,r]:e},[]),null==s?void 0:s.class,null==s?void 0:s.className)}}},e=>{var t=t=>e(e.s=t);e.O(0,[449,9222,7651,2913,4499,7358],()=>t(67013)),_N_E=e.O()}]);
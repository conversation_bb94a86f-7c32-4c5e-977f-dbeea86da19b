(()=>{var e={};e.id=770,e.ids=[770],e.modules={545:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SegmentConfigOutSerializer=void 0,t.SegmentConfigOutSerializer={_fromJsonObject:e=>({}),_toJsonObject:e=>({})}},2289:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MessageOutSerializer=void 0,t.MessageOutSerializer={_fromJsonObject:e=>({channels:e.channels,eventId:e.eventId,eventType:e.eventType,id:e.id,payload:e.payload,tags:e.tags,timestamp:new Date(e.timestamp)}),_toJsonObject:e=>({channels:e.channels,eventId:e.eventId,eventType:e.eventType,id:e.id,payload:e.payload,tags:e.tags,timestamp:e.timestamp})}},2585:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EventExampleInSerializer=void 0,t.EventExampleInSerializer={_fromJsonObject:e=>({eventType:e.eventType,exampleIndex:e.exampleIndex}),_toJsonObject:e=>({eventType:e.eventType,exampleIndex:e.exampleIndex})}},2831:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MessageStatusSerializer=t.MessageStatus=void 0,function(e){e[e.Success=0]="Success",e[e.Pending=1]="Pending",e[e.Fail=2]="Fail",e[e.Sending=3]="Sending"}(t.MessageStatus||(t.MessageStatus={})),t.MessageStatusSerializer={_fromJsonObject:e=>e,_toJsonObject:e=>e}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4053:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EndpointOutSerializer=void 0,t.EndpointOutSerializer={_fromJsonObject:e=>({channels:e.channels,createdAt:new Date(e.createdAt),description:e.description,disabled:e.disabled,filterTypes:e.filterTypes,id:e.id,metadata:e.metadata,rateLimit:e.rateLimit,uid:e.uid,updatedAt:new Date(e.updatedAt),url:e.url,version:e.version}),_toJsonObject:e=>({channels:e.channels,createdAt:e.createdAt,description:e.description,disabled:e.disabled,filterTypes:e.filterTypes,id:e.id,metadata:e.metadata,rateLimit:e.rateLimit,uid:e.uid,updatedAt:e.updatedAt,url:e.url,version:e.version})}},4573:e=>{"use strict";e.exports=require("node:buffer")},5326:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Ingest=void 0;let i=r(69682),a=r(74378),s=r(33850),o=r(62315),n=r(30359);class d{constructor(e){this.requestCtx=e}get endpoint(){return new o.IngestEndpoint(this.requestCtx)}get source(){return new n.IngestSource(this.requestCtx)}dashboard(e,t,r){let o=new s.SvixRequest(s.HttpMethod.POST,"/ingest/api/v1/source/{source_id}/dashboard");return o.setPathParam("source_id",e),o.setHeaderParam("idempotency-key",null==r?void 0:r.idempotencyKey),o.setBody(a.IngestSourceConsumerPortalAccessInSerializer._toJsonObject(t)),o.send(this.requestCtx,i.DashboardAccessOutSerializer._fromJsonObject)}}t.Ingest=d},6676:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EndpointMessageOutSerializer=void 0;let i=r(2831);t.EndpointMessageOutSerializer={_fromJsonObject:e=>({channels:e.channels,eventId:e.eventId,eventType:e.eventType,id:e.id,nextAttempt:e.nextAttempt?new Date(e.nextAttempt):null,payload:e.payload,status:i.MessageStatusSerializer._fromJsonObject(e.status),tags:e.tags,timestamp:new Date(e.timestamp)}),_toJsonObject:e=>({channels:e.channels,eventId:e.eventId,eventType:e.eventType,id:e.id,nextAttempt:e.nextAttempt,payload:e.payload,status:i.MessageStatusSerializer._toJsonObject(e.status),tags:e.tags,timestamp:e.timestamp})}},8086:e=>{"use strict";e.exports=require("module")},9677:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.GithubConfigOutSerializer=void 0,t.GithubConfigOutSerializer={_fromJsonObject:e=>({}),_toJsonObject:e=>({})}},9927:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EventTypeImportOpenApiOutDataSerializer=void 0;let i=r(62308);t.EventTypeImportOpenApiOutDataSerializer={_fromJsonObject(e){var t;return{modified:e.modified,toModify:null==(t=e.to_modify)?void 0:t.map(e=>i.EventTypeFromOpenApiSerializer._fromJsonObject(e))}},_toJsonObject(e){var t;return{modified:e.modified,to_modify:null==(t=e.toModify)?void 0:t.map(e=>i.EventTypeFromOpenApiSerializer._toJsonObject(e))}}}},10451:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Endpoint=void 0;let i=r(47512),a=r(67195),s=r(19602),o=r(34166),n=r(4053),d=r(36583),p=r(42725),u=r(30433),c=r(51690),l=r(31815),m=r(52118),_=r(85436),O=r(2585),b=r(60528),v=r(2289),f=r(27997),g=r(80320),h=r(44200),y=r(72235),S=r(33850);class P{constructor(e){this.requestCtx=e}list(e,t){let r=new S.SvixRequest(S.HttpMethod.GET,"/api/v1/app/{app_id}/endpoint");return r.setPathParam("app_id",e),r.setQueryParam("limit",null==t?void 0:t.limit),r.setQueryParam("iterator",null==t?void 0:t.iterator),r.setQueryParam("order",null==t?void 0:t.order),r.send(this.requestCtx,b.ListResponseEndpointOutSerializer._fromJsonObject)}create(e,t,r){let i=new S.SvixRequest(S.HttpMethod.POST,"/api/v1/app/{app_id}/endpoint");return i.setPathParam("app_id",e),i.setHeaderParam("idempotency-key",null==r?void 0:r.idempotencyKey),i.setBody(o.EndpointInSerializer._toJsonObject(t)),i.send(this.requestCtx,n.EndpointOutSerializer._fromJsonObject)}get(e,t){let r=new S.SvixRequest(S.HttpMethod.GET,"/api/v1/app/{app_id}/endpoint/{endpoint_id}");return r.setPathParam("app_id",e),r.setPathParam("endpoint_id",t),r.send(this.requestCtx,n.EndpointOutSerializer._fromJsonObject)}update(e,t,r){let i=new S.SvixRequest(S.HttpMethod.PUT,"/api/v1/app/{app_id}/endpoint/{endpoint_id}");return i.setPathParam("app_id",e),i.setPathParam("endpoint_id",t),i.setBody(_.EndpointUpdateSerializer._toJsonObject(r)),i.send(this.requestCtx,n.EndpointOutSerializer._fromJsonObject)}delete(e,t){let r=new S.SvixRequest(S.HttpMethod.DELETE,"/api/v1/app/{app_id}/endpoint/{endpoint_id}");return r.setPathParam("app_id",e),r.setPathParam("endpoint_id",t),r.sendNoResponseBody(this.requestCtx)}patch(e,t,r){let i=new S.SvixRequest(S.HttpMethod.PATCH,"/api/v1/app/{app_id}/endpoint/{endpoint_id}");return i.setPathParam("app_id",e),i.setPathParam("endpoint_id",t),i.setBody(d.EndpointPatchSerializer._toJsonObject(r)),i.send(this.requestCtx,n.EndpointOutSerializer._fromJsonObject)}getHeaders(e,t){let r=new S.SvixRequest(S.HttpMethod.GET,"/api/v1/app/{app_id}/endpoint/{endpoint_id}/headers");return r.setPathParam("app_id",e),r.setPathParam("endpoint_id",t),r.send(this.requestCtx,a.EndpointHeadersOutSerializer._fromJsonObject)}updateHeaders(e,t,r){let a=new S.SvixRequest(S.HttpMethod.PUT,"/api/v1/app/{app_id}/endpoint/{endpoint_id}/headers");return a.setPathParam("app_id",e),a.setPathParam("endpoint_id",t),a.setBody(i.EndpointHeadersInSerializer._toJsonObject(r)),a.sendNoResponseBody(this.requestCtx)}headersUpdate(e,t,r){return this.updateHeaders(e,t,r)}patchHeaders(e,t,r){let i=new S.SvixRequest(S.HttpMethod.PATCH,"/api/v1/app/{app_id}/endpoint/{endpoint_id}/headers");return i.setPathParam("app_id",e),i.setPathParam("endpoint_id",t),i.setBody(s.EndpointHeadersPatchInSerializer._toJsonObject(r)),i.sendNoResponseBody(this.requestCtx)}headersPatch(e,t,r){return this.patchHeaders(e,t,r)}recover(e,t,r,i){let a=new S.SvixRequest(S.HttpMethod.POST,"/api/v1/app/{app_id}/endpoint/{endpoint_id}/recover");return a.setPathParam("app_id",e),a.setPathParam("endpoint_id",t),a.setHeaderParam("idempotency-key",null==i?void 0:i.idempotencyKey),a.setBody(f.RecoverInSerializer._toJsonObject(r)),a.send(this.requestCtx,g.RecoverOutSerializer._fromJsonObject)}replayMissing(e,t,r,i){let a=new S.SvixRequest(S.HttpMethod.POST,"/api/v1/app/{app_id}/endpoint/{endpoint_id}/replay-missing");return a.setPathParam("app_id",e),a.setPathParam("endpoint_id",t),a.setHeaderParam("idempotency-key",null==i?void 0:i.idempotencyKey),a.setBody(h.ReplayInSerializer._toJsonObject(r)),a.send(this.requestCtx,y.ReplayOutSerializer._fromJsonObject)}getSecret(e,t){let r=new S.SvixRequest(S.HttpMethod.GET,"/api/v1/app/{app_id}/endpoint/{endpoint_id}/secret");return r.setPathParam("app_id",e),r.setPathParam("endpoint_id",t),r.send(this.requestCtx,p.EndpointSecretOutSerializer._fromJsonObject)}rotateSecret(e,t,r,i){let a=new S.SvixRequest(S.HttpMethod.POST,"/api/v1/app/{app_id}/endpoint/{endpoint_id}/secret/rotate");return a.setPathParam("app_id",e),a.setPathParam("endpoint_id",t),a.setHeaderParam("idempotency-key",null==i?void 0:i.idempotencyKey),a.setBody(u.EndpointSecretRotateInSerializer._toJsonObject(r)),a.sendNoResponseBody(this.requestCtx)}sendExample(e,t,r,i){let a=new S.SvixRequest(S.HttpMethod.POST,"/api/v1/app/{app_id}/endpoint/{endpoint_id}/send-example");return a.setPathParam("app_id",e),a.setPathParam("endpoint_id",t),a.setHeaderParam("idempotency-key",null==i?void 0:i.idempotencyKey),a.setBody(O.EventExampleInSerializer._toJsonObject(r)),a.send(this.requestCtx,v.MessageOutSerializer._fromJsonObject)}getStats(e,t,r){let i=new S.SvixRequest(S.HttpMethod.GET,"/api/v1/app/{app_id}/endpoint/{endpoint_id}/stats");return i.setPathParam("app_id",e),i.setPathParam("endpoint_id",t),i.setQueryParam("since",null==r?void 0:r.since),i.setQueryParam("until",null==r?void 0:r.until),i.send(this.requestCtx,c.EndpointStatsSerializer._fromJsonObject)}transformationGet(e,t){let r=new S.SvixRequest(S.HttpMethod.GET,"/api/v1/app/{app_id}/endpoint/{endpoint_id}/transformation");return r.setPathParam("app_id",e),r.setPathParam("endpoint_id",t),r.send(this.requestCtx,m.EndpointTransformationOutSerializer._fromJsonObject)}transformationPartialUpdate(e,t,r){let i=new S.SvixRequest(S.HttpMethod.PATCH,"/api/v1/app/{app_id}/endpoint/{endpoint_id}/transformation");return i.setPathParam("app_id",e),i.setPathParam("endpoint_id",t),i.setBody(l.EndpointTransformationInSerializer._toJsonObject(r)),i.sendNoResponseBody(this.requestCtx)}}t.Endpoint=P},10512:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.OrderingSerializer=t.Ordering=void 0,function(e){e.Ascending="ascending",e.Descending="descending"}(t.Ordering||(t.Ordering={})),t.OrderingSerializer={_fromJsonObject:e=>e,_toJsonObject:e=>e}},10755:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.OperationalWebhookEndpointHeadersInSerializer=void 0,t.OperationalWebhookEndpointHeadersInSerializer={_fromJsonObject:e=>({headers:e.headers}),_toJsonObject:e=>({headers:e.headers})}},10764:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AdobeSignConfigOutSerializer=void 0,t.AdobeSignConfigOutSerializer={_fromJsonObject:e=>({}),_toJsonObject:e=>({})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},11723:e=>{"use strict";e.exports=require("querystring")},11997:e=>{"use strict";e.exports=require("punycode")},12412:e=>{"use strict";e.exports=require("assert")},12493:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Management=void 0;let i=r(16046);class a{constructor(e){this.requestCtx=e}get authentication(){return new i.ManagementAuthentication(this.requestCtx)}}t.Management=a},13440:e=>{"use strict";e.exports=require("util/types")},13535:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.StripeConfigSerializer=void 0,t.StripeConfigSerializer={_fromJsonObject:e=>({secret:e.secret}),_toJsonObject:e=>({secret:e.secret})}},13723:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.BackgroundTask=void 0;let i=r(39954),a=r(64624),s=r(33850);class o{constructor(e){this.requestCtx=e}list(e){let t=new s.SvixRequest(s.HttpMethod.GET,"/api/v1/background-task");return t.setQueryParam("status",null==e?void 0:e.status),t.setQueryParam("task",null==e?void 0:e.task),t.setQueryParam("limit",null==e?void 0:e.limit),t.setQueryParam("iterator",null==e?void 0:e.iterator),t.setQueryParam("order",null==e?void 0:e.order),t.send(this.requestCtx,a.ListResponseBackgroundTaskOutSerializer._fromJsonObject)}listByEndpoint(e){return this.list(e)}get(e){let t=new s.SvixRequest(s.HttpMethod.GET,"/api/v1/background-task/{task_id}");return t.setPathParam("task_id",e),t.send(this.requestCtx,i.BackgroundTaskOutSerializer._fromJsonObject)}}t.BackgroundTask=o},14593:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.BackgroundTaskTypeSerializer=t.BackgroundTaskType=void 0,function(e){e.EndpointReplay="endpoint.replay",e.EndpointRecover="endpoint.recover",e.ApplicationStats="application.stats",e.MessageBroadcast="message.broadcast",e.SdkGenerate="sdk.generate",e.EventTypeAggregate="event-type.aggregate",e.ApplicationPurgeContent="application.purge_content"}(t.BackgroundTaskType||(t.BackgroundTaskType={})),t.BackgroundTaskTypeSerializer={_fromJsonObject:e=>e,_toJsonObject:e=>e}},15760:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ApiTokenCensoredOutSerializer=void 0,t.ApiTokenCensoredOutSerializer={_fromJsonObject:e=>({censoredToken:e.censoredToken,createdAt:new Date(e.createdAt),expiresAt:e.expiresAt?new Date(e.expiresAt):null,id:e.id,name:e.name,scopes:e.scopes}),_toJsonObject:e=>({censoredToken:e.censoredToken,createdAt:e.createdAt,expiresAt:e.expiresAt,id:e.id,name:e.name,scopes:e.scopes})}},16046:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ManagementAuthentication=void 0;let i=r(90493),a=r(88502),s=r(57205),o=r(20953),n=r(33850);class d{constructor(e){this.requestCtx=e}listApiTokens(e){let t=new n.SvixRequest(n.HttpMethod.GET,"/api/v1/management/authentication/api-token");return t.setQueryParam("limit",null==e?void 0:e.limit),t.setQueryParam("iterator",null==e?void 0:e.iterator),t.setQueryParam("order",null==e?void 0:e.order),t.send(this.requestCtx,o.ListResponseApiTokenCensoredOutSerializer._fromJsonObject)}createApiToken(e,t){let r=new n.SvixRequest(n.HttpMethod.POST,"/api/v1/management/authentication/api-token");return r.setHeaderParam("idempotency-key",null==t?void 0:t.idempotencyKey),r.setBody(a.ApiTokenInSerializer._toJsonObject(e)),r.send(this.requestCtx,s.ApiTokenOutSerializer._fromJsonObject)}expireApiToken(e,t,r){let a=new n.SvixRequest(n.HttpMethod.POST,"/api/v1/management/authentication/api-token/{key_id}/expire");return a.setPathParam("key_id",e),a.setHeaderParam("idempotency-key",null==r?void 0:r.idempotencyKey),a.setBody(i.ApiTokenExpireInSerializer._toJsonObject(t)),a.sendNoResponseBody(this.requestCtx)}}t.ManagementAuthentication=d},16141:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.OperationalWebhookEndpointUpdateSerializer=void 0,t.OperationalWebhookEndpointUpdateSerializer={_fromJsonObject:e=>({description:e.description,disabled:e.disabled,filterTypes:e.filterTypes,metadata:e.metadata,rateLimit:e.rateLimit,uid:e.uid,url:e.url}),_toJsonObject:e=>({description:e.description,disabled:e.disabled,filterTypes:e.filterTypes,metadata:e.metadata,rateLimit:e.rateLimit,uid:e.uid,url:e.url})}},16456:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ListResponseIngestEndpointOutSerializer=void 0;let i=r(49353);t.ListResponseIngestEndpointOutSerializer={_fromJsonObject:e=>({data:e.data.map(e=>i.IngestEndpointOutSerializer._fromJsonObject(e)),done:e.done,iterator:e.iterator,prevIterator:e.prevIterator}),_toJsonObject:e=>({data:e.data.map(e=>i.IngestEndpointOutSerializer._toJsonObject(e)),done:e.done,iterator:e.iterator,prevIterator:e.prevIterator})}},16698:e=>{"use strict";e.exports=require("node:async_hooks")},17130:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.OperationalWebhookEndpointHeadersOutSerializer=void 0,t.OperationalWebhookEndpointHeadersOutSerializer={_fromJsonObject:e=>({headers:e.headers,sensitive:e.sensitive}),_toJsonObject:e=>({headers:e.headers,sensitive:e.sensitive})}},17211:function(e,t,r){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,r,i){void 0===i&&(i=r);var a=Object.getOwnPropertyDescriptor(t,r);(!a||("get"in a?!t.__esModule:a.writable||a.configurable))&&(a={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,i,a)}:function(e,t,r,i){void 0===i&&(i=r),e[i]=t[r]}),a=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||i(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),t.Svix=t.messageInRaw=t.ValidationError=t.HttpErrorOut=t.HTTPValidationError=t.ApiException=void 0;let s=r(76546),o=r(63094),n=r(13723),d=r(10451),p=r(27776),u=r(5326),c=r(85710),l=r(12493),m=r(20523),_=r(47278),O=r(44467),b=r(54638),v=r(63873);var f=r(32123);Object.defineProperty(t,"ApiException",{enumerable:!0,get:function(){return f.ApiException}});var g=r(92352);Object.defineProperty(t,"HTTPValidationError",{enumerable:!0,get:function(){return g.HTTPValidationError}}),Object.defineProperty(t,"HttpErrorOut",{enumerable:!0,get:function(){return g.HttpErrorOut}}),Object.defineProperty(t,"ValidationError",{enumerable:!0,get:function(){return g.ValidationError}}),a(r(86730),t),a(r(69342),t);var h=r(20523);Object.defineProperty(t,"messageInRaw",{enumerable:!0,get:function(){return h.messageInRaw}});let y=[{region:"us",url:"https://api.us.svix.com"},{region:"eu",url:"https://api.eu.svix.com"},{region:"in",url:"https://api.in.svix.com"},{region:"ca",url:"https://api.ca.svix.com"},{region:"au",url:"https://api.au.svix.com"}];class S{constructor(e,t={}){var r,i,a;let s=null==(r=y.find(t=>t.region===e.split(".")[1]))?void 0:r.url,o=null!=(a=null!=(i=t.serverUrl)?i:s)?a:"https://api.svix.com";this.requestCtx={baseUrl:o,token:e,timeout:t.requestTimeout}}get authentication(){return new s.Authentication(this.requestCtx)}get application(){return new o.Application(this.requestCtx)}get endpoint(){return new d.Endpoint(this.requestCtx)}get eventType(){return new p.EventType(this.requestCtx)}get ingest(){return new u.Ingest(this.requestCtx)}get integration(){return new c.Integration(this.requestCtx)}get management(){return new l.Management(this.requestCtx)}get message(){return new m.Message(this.requestCtx)}get messageAttempt(){return new _.MessageAttempt(this.requestCtx)}get backgroundTask(){return new n.BackgroundTask(this.requestCtx)}get statistics(){return new v.Statistics(this.requestCtx)}get operationalWebhook(){return new O.OperationalWebhook(this.requestCtx)}get operationalWebhookEndpoint(){return new b.OperationalWebhookEndpoint(this.requestCtx)}}t.Svix=S},17895:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.StatusCodeClassSerializer=t.StatusCodeClass=void 0,function(e){e[e.CodeNone=0]="CodeNone",e[e.Code1xx=100]="Code1xx",e[e.Code2xx=200]="Code2xx",e[e.Code3xx=300]="Code3xx",e[e.Code4xx=400]="Code4xx",e[e.Code5xx=500]="Code5xx"}(t.StatusCodeClass||(t.StatusCodeClass={})),t.StatusCodeClassSerializer={_fromJsonObject:e=>e,_toJsonObject:e=>e}},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19602:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EndpointHeadersPatchInSerializer=void 0,t.EndpointHeadersPatchInSerializer={_fromJsonObject:e=>({headers:e.headers}),_toJsonObject:e=>({headers:e.headers})}},19771:e=>{"use strict";e.exports=require("process")},19921:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ZoomConfigOutSerializer=void 0,t.ZoomConfigOutSerializer={_fromJsonObject:e=>({}),_toJsonObject:e=>({})}},20013:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.IntegrationUpdateSerializer=void 0,t.IntegrationUpdateSerializer={_fromJsonObject:e=>({featureFlags:e.featureFlags,name:e.name}),_toJsonObject:e=>({featureFlags:e.featureFlags,name:e.name})}},20523:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.messageInRaw=t.Message=void 0;let i=r(72565),a=r(24830),s=r(90730),o=r(2289),n=r(33850),d=r(78889);class p{constructor(e){this.requestCtx=e}get poller(){return new d.MessagePoller(this.requestCtx)}list(e,t){let r=new n.SvixRequest(n.HttpMethod.GET,"/api/v1/app/{app_id}/msg");return r.setPathParam("app_id",e),r.setQueryParam("limit",null==t?void 0:t.limit),r.setQueryParam("iterator",null==t?void 0:t.iterator),r.setQueryParam("channel",null==t?void 0:t.channel),r.setQueryParam("before",null==t?void 0:t.before),r.setQueryParam("after",null==t?void 0:t.after),r.setQueryParam("with_content",null==t?void 0:t.withContent),r.setQueryParam("tag",null==t?void 0:t.tag),r.setQueryParam("event_types",null==t?void 0:t.eventTypes),r.send(this.requestCtx,a.ListResponseMessageOutSerializer._fromJsonObject)}create(e,t,r){let i=new n.SvixRequest(n.HttpMethod.POST,"/api/v1/app/{app_id}/msg");return i.setPathParam("app_id",e),i.setQueryParam("with_content",null==r?void 0:r.withContent),i.setHeaderParam("idempotency-key",null==r?void 0:r.idempotencyKey),i.setBody(s.MessageInSerializer._toJsonObject(t)),i.send(this.requestCtx,o.MessageOutSerializer._fromJsonObject)}expungeAllContents(e,t){let r=new n.SvixRequest(n.HttpMethod.POST,"/api/v1/app/{app_id}/msg/expunge-all-contents");return r.setPathParam("app_id",e),r.setHeaderParam("idempotency-key",null==t?void 0:t.idempotencyKey),r.send(this.requestCtx,i.ExpungeAllContentsOutSerializer._fromJsonObject)}get(e,t,r){let i=new n.SvixRequest(n.HttpMethod.GET,"/api/v1/app/{app_id}/msg/{msg_id}");return i.setPathParam("app_id",e),i.setPathParam("msg_id",t),i.setQueryParam("with_content",null==r?void 0:r.withContent),i.send(this.requestCtx,o.MessageOutSerializer._fromJsonObject)}expungeContent(e,t){let r=new n.SvixRequest(n.HttpMethod.DELETE,"/api/v1/app/{app_id}/msg/{msg_id}/content");return r.setPathParam("app_id",e),r.setPathParam("msg_id",t),r.sendNoResponseBody(this.requestCtx)}}t.Message=p,t.messageInRaw=function(e,t,r){return{eventType:e,payload:{},transformationsParams:{rawPayload:t,headers:r?{"content-type":r}:void 0}}}},20953:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ListResponseApiTokenCensoredOutSerializer=void 0;let i=r(15760);t.ListResponseApiTokenCensoredOutSerializer={_fromJsonObject:e=>({data:e.data.map(e=>i.ApiTokenCensoredOutSerializer._fromJsonObject(e)),done:e.done,iterator:e.iterator,prevIterator:e.prevIterator}),_toJsonObject:e=>({data:e.data.map(e=>i.ApiTokenCensoredOutSerializer._toJsonObject(e)),done:e.done,iterator:e.iterator,prevIterator:e.prevIterator})}},21439:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.HubspotConfigSerializer=void 0,t.HubspotConfigSerializer={_fromJsonObject:e=>({secret:e.secret}),_toJsonObject:e=>({secret:e.secret})}},21820:e=>{"use strict";e.exports=require("os")},22235:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ApplicationInSerializer=void 0,t.ApplicationInSerializer={_fromJsonObject:e=>({metadata:e.metadata,name:e.name,rateLimit:e.rateLimit,uid:e.uid}),_toJsonObject:e=>({metadata:e.metadata,name:e.name,rateLimit:e.rateLimit,uid:e.uid})}},22434:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ApplicationOutSerializer=void 0,t.ApplicationOutSerializer={_fromJsonObject:e=>({createdAt:new Date(e.createdAt),id:e.id,metadata:e.metadata,name:e.name,rateLimit:e.rateLimit,uid:e.uid,updatedAt:new Date(e.updatedAt)}),_toJsonObject:e=>({createdAt:e.createdAt,id:e.id,metadata:e.metadata,name:e.name,rateLimit:e.rateLimit,uid:e.uid,updatedAt:e.updatedAt})}},22783:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AppUsageStatsOutSerializer=void 0;let i=r(92091),a=r(14593);t.AppUsageStatsOutSerializer={_fromJsonObject:e=>({id:e.id,status:i.BackgroundTaskStatusSerializer._fromJsonObject(e.status),task:a.BackgroundTaskTypeSerializer._fromJsonObject(e.task),unresolvedAppIds:e.unresolvedAppIds}),_toJsonObject:e=>({id:e.id,status:i.BackgroundTaskStatusSerializer._toJsonObject(e.status),task:a.BackgroundTaskTypeSerializer._toJsonObject(e.task),unresolvedAppIds:e.unresolvedAppIds})}},23167:(e,t,r)=>{"use strict";r.d(t,{H:()=>s});var i=r(71166),a=r(25);let s=()=>(0,i.w)({server:{DATABASE_URL:a.z.string().url()},runtimeEnv:{DATABASE_URL:process.env.DATABASE_URL}})},24458:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.IngestEndpointSecretInSerializer=void 0,t.IngestEndpointSecretInSerializer={_fromJsonObject:e=>({key:e.key}),_toJsonObject:e=>({key:e.key})}},24830:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ListResponseMessageOutSerializer=void 0;let i=r(2289);t.ListResponseMessageOutSerializer={_fromJsonObject:e=>({data:e.data.map(e=>i.MessageOutSerializer._fromJsonObject(e)),done:e.done,iterator:e.iterator,prevIterator:e.prevIterator}),_toJsonObject:e=>({data:e.data.map(e=>i.MessageOutSerializer._toJsonObject(e)),done:e.done,iterator:e.iterator,prevIterator:e.prevIterator})}},24993:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PollingEndpointMessageOutSerializer=void 0,t.PollingEndpointMessageOutSerializer={_fromJsonObject:e=>({channels:e.channels,eventId:e.eventId,eventType:e.eventType,headers:e.headers,id:e.id,payload:e.payload,tags:e.tags,timestamp:new Date(e.timestamp)}),_toJsonObject:e=>({channels:e.channels,eventId:e.eventId,eventType:e.eventType,headers:e.headers,id:e.id,payload:e.payload,tags:e.tags,timestamp:e.timestamp})}},26605:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EventTypeInSerializer=void 0,t.EventTypeInSerializer={_fromJsonObject:e=>({archived:e.archived,deprecated:e.deprecated,description:e.description,featureFlag:e.featureFlag,groupName:e.groupName,name:e.name,schemas:e.schemas}),_toJsonObject:e=>({archived:e.archived,deprecated:e.deprecated,description:e.description,featureFlag:e.featureFlag,groupName:e.groupName,name:e.name,schemas:e.schemas})}},27070:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DocusignConfigSerializer=void 0,t.DocusignConfigSerializer={_fromJsonObject:e=>({secret:e.secret}),_toJsonObject:e=>({secret:e.secret})}},27776:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EventType=void 0;let i=r(89572),a=r(73455),s=r(26605),o=r(78480),n=r(36162),d=r(29155),p=r(87063),u=r(33850);class c{constructor(e){this.requestCtx=e}list(e){let t=new u.SvixRequest(u.HttpMethod.GET,"/api/v1/event-type");return t.setQueryParam("limit",null==e?void 0:e.limit),t.setQueryParam("iterator",null==e?void 0:e.iterator),t.setQueryParam("order",null==e?void 0:e.order),t.setQueryParam("include_archived",null==e?void 0:e.includeArchived),t.setQueryParam("with_content",null==e?void 0:e.withContent),t.send(this.requestCtx,p.ListResponseEventTypeOutSerializer._fromJsonObject)}create(e,t){let r=new u.SvixRequest(u.HttpMethod.POST,"/api/v1/event-type");return r.setHeaderParam("idempotency-key",null==t?void 0:t.idempotencyKey),r.setBody(s.EventTypeInSerializer._toJsonObject(e)),r.send(this.requestCtx,o.EventTypeOutSerializer._fromJsonObject)}importOpenapi(e,t){let r=new u.SvixRequest(u.HttpMethod.POST,"/api/v1/event-type/import/openapi");return r.setHeaderParam("idempotency-key",null==t?void 0:t.idempotencyKey),r.setBody(i.EventTypeImportOpenApiInSerializer._toJsonObject(e)),r.send(this.requestCtx,a.EventTypeImportOpenApiOutSerializer._fromJsonObject)}get(e){let t=new u.SvixRequest(u.HttpMethod.GET,"/api/v1/event-type/{event_type_name}");return t.setPathParam("event_type_name",e),t.send(this.requestCtx,o.EventTypeOutSerializer._fromJsonObject)}update(e,t){let r=new u.SvixRequest(u.HttpMethod.PUT,"/api/v1/event-type/{event_type_name}");return r.setPathParam("event_type_name",e),r.setBody(d.EventTypeUpdateSerializer._toJsonObject(t)),r.send(this.requestCtx,o.EventTypeOutSerializer._fromJsonObject)}delete(e,t){let r=new u.SvixRequest(u.HttpMethod.DELETE,"/api/v1/event-type/{event_type_name}");return r.setPathParam("event_type_name",e),r.setQueryParam("expunge",null==t?void 0:t.expunge),r.sendNoResponseBody(this.requestCtx)}patch(e,t){let r=new u.SvixRequest(u.HttpMethod.PATCH,"/api/v1/event-type/{event_type_name}");return r.setPathParam("event_type_name",e),r.setBody(n.EventTypePatchSerializer._toJsonObject(t)),r.send(this.requestCtx,o.EventTypeOutSerializer._fromJsonObject)}}t.EventType=c},27910:e=>{"use strict";e.exports=require("stream")},27997:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.RecoverInSerializer=void 0,t.RecoverInSerializer={_fromJsonObject:e=>({since:new Date(e.since),until:e.until?new Date(e.until):null}),_toJsonObject:e=>({since:e.since,until:e.until})}},28287:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PollingEndpointConsumerSeekInSerializer=void 0,t.PollingEndpointConsumerSeekInSerializer={_fromJsonObject:e=>({after:new Date(e.after)}),_toJsonObject:e=>({after:e.after})}},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29155:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EventTypeUpdateSerializer=void 0,t.EventTypeUpdateSerializer={_fromJsonObject:e=>({archived:e.archived,deprecated:e.deprecated,description:e.description,featureFlag:e.featureFlag,groupName:e.groupName,schemas:e.schemas}),_toJsonObject:e=>({archived:e.archived,deprecated:e.deprecated,description:e.description,featureFlag:e.featureFlag,groupName:e.groupName,schemas:e.schemas})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30278:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SlackConfigOutSerializer=void 0,t.SlackConfigOutSerializer={_fromJsonObject:e=>({}),_toJsonObject:e=>({})}},30359:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.IngestSource=void 0;let i=r(45365),a=r(48105),s=r(74788),o=r(56420),n=r(33850);class d{constructor(e){this.requestCtx=e}list(e){let t=new n.SvixRequest(n.HttpMethod.GET,"/ingest/api/v1/source");return t.setQueryParam("limit",null==e?void 0:e.limit),t.setQueryParam("iterator",null==e?void 0:e.iterator),t.setQueryParam("order",null==e?void 0:e.order),t.send(this.requestCtx,s.ListResponseIngestSourceOutSerializer._fromJsonObject)}create(e,t){let r=new n.SvixRequest(n.HttpMethod.POST,"/ingest/api/v1/source");return r.setHeaderParam("idempotency-key",null==t?void 0:t.idempotencyKey),r.setBody(i.IngestSourceInSerializer._toJsonObject(e)),r.send(this.requestCtx,a.IngestSourceOutSerializer._fromJsonObject)}get(e){let t=new n.SvixRequest(n.HttpMethod.GET,"/ingest/api/v1/source/{source_id}");return t.setPathParam("source_id",e),t.send(this.requestCtx,a.IngestSourceOutSerializer._fromJsonObject)}update(e,t){let r=new n.SvixRequest(n.HttpMethod.PUT,"/ingest/api/v1/source/{source_id}");return r.setPathParam("source_id",e),r.setBody(i.IngestSourceInSerializer._toJsonObject(t)),r.send(this.requestCtx,a.IngestSourceOutSerializer._fromJsonObject)}delete(e){let t=new n.SvixRequest(n.HttpMethod.DELETE,"/ingest/api/v1/source/{source_id}");return t.setPathParam("source_id",e),t.sendNoResponseBody(this.requestCtx)}rotateToken(e,t){let r=new n.SvixRequest(n.HttpMethod.POST,"/ingest/api/v1/source/{source_id}/token/rotate");return r.setPathParam("source_id",e),r.setHeaderParam("idempotency-key",null==t?void 0:t.idempotencyKey),r.send(this.requestCtx,o.RotateTokenOutSerializer._fromJsonObject)}}t.IngestSource=d},30433:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EndpointSecretRotateInSerializer=void 0,t.EndpointSecretRotateInSerializer={_fromJsonObject:e=>({key:e.key}),_toJsonObject:e=>({key:e.key})}},30704:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DocusignConfigOutSerializer=void 0,t.DocusignConfigOutSerializer={_fromJsonObject:e=>({}),_toJsonObject:e=>({})}},31421:e=>{"use strict";e.exports=require("node:child_process")},31815:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EndpointTransformationInSerializer=void 0,t.EndpointTransformationInSerializer={_fromJsonObject:e=>({code:e.code,enabled:e.enabled}),_toJsonObject:e=>({code:e.code,enabled:e.enabled})}},32123:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ApiException=void 0;class r extends Error{constructor(e,t,r){super(`HTTP-Code: ${e}
Headers: ${JSON.stringify(r)}`),this.code=e,this.body=t,this.headers={},r.forEach((e,t)=>{this.headers[t]=e})}}t.ApiException=r},32721:(e,t,r)=>{"use strict";var i=r(81630),a=r(55591),s=r(77539);let o=new i.Agent({keepAlive:!0}),n=new a.Agent({keepAlive:!0}),d=function(e){return"http:"==e.protocol?o:n};e.exports=function(e,t){return/^\/\//.test(e)&&(e="https:"+e),s.call(this,e,{agent:d,...t})},global.fetch||(global.fetch=e.exports,global.Response=s.Response,global.Headers=s.Headers,global.Request=s.Request)},33398:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AppPortalAccessInSerializer=void 0;let i=r(22235);t.AppPortalAccessInSerializer={_fromJsonObject:e=>({application:e.application?i.ApplicationInSerializer._fromJsonObject(e.application):void 0,expiry:e.expiry,featureFlags:e.featureFlags,readOnly:e.readOnly}),_toJsonObject:e=>({application:e.application?i.ApplicationInSerializer._toJsonObject(e.application):void 0,expiry:e.expiry,featureFlags:e.featureFlags,readOnly:e.readOnly})}},33850:function(e,t,r){"use strict";var i,a=this&&this.__awaiter||function(e,t,r,i){return new(r||(r=Promise))(function(a,s){function o(e){try{d(i.next(e))}catch(e){s(e)}}function n(e){try{d(i.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(o,n)}d((i=i.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0}),t.SvixRequest=t.HttpMethod=t.LIB_VERSION=void 0,r(32721);let s=r(32123);t.LIB_VERSION="1.66.0";let o=`svix-libs/${t.LIB_VERSION}/javascript`;(i=t.HttpMethod||(t.HttpMethod={})).GET="GET",i.HEAD="HEAD",i.POST="POST",i.PUT="PUT",i.DELETE="DELETE",i.CONNECT="CONNECT",i.OPTIONS="OPTIONS",i.TRACE="TRACE",i.PATCH="PATCH";class n{constructor(e,t){this.method=e,this.path=t,this.queryParams={},this.headerParams={}}setPathParam(e,t){let r=this.path.replace(`{${e}}`,encodeURIComponent(t));if(this.path===r)throw Error(`path parameter ${e} not found`);this.path=r}setQueryParam(e,t){if(null!=t)if("string"==typeof t)this.queryParams[e]=t;else if("boolean"==typeof t||"number"==typeof t)this.queryParams[e]=t.toString();else if(t instanceof Date)this.queryParams[e]=t.toISOString();else if(t instanceof Array)t.length>0&&(this.queryParams[e]=t.join(","));else throw Error(`query parameter ${e} has unsupported type`)}setHeaderParam(e,t){void 0!==t&&(this.headerParams[e]=t)}setBody(e){this.body=JSON.stringify(e)}send(e,t){return a(this,void 0,void 0,function*(){let r=yield this.sendInner(e);return 204==r.status?null:t(JSON.parse((yield r.text())))})}sendNoResponseBody(e){return a(this,void 0,void 0,function*(){yield this.sendInner(e)})}sendInner(e){return a(this,void 0,void 0,function*(){let t=new URL(e.baseUrl+this.path);for(let[e,r]of Object.entries(this.queryParams))t.searchParams.set(e,r);let r=Math.floor(Math.random()*Number.MAX_SAFE_INTEGER);null!=this.body&&(this.headerParams["content-type"]="application/json");let i="credentials"in Request.prototype;return function(e){return a(this,void 0,void 0,function*(){if(e.status<300)return e;let t=yield e.text();if(422===e.status||e.status>=400&&e.status<=499)throw new s.ApiException(e.status,JSON.parse(t),e.headers);throw new s.ApiException(e.status,t,e.headers)})}((yield function e(t,r,i=2,s=50,o=1){return a(this,void 0,void 0,function*(){try{let e=yield fetch(t,r);if(i<=0||e.status<500)return e}catch(e){if(i<=0)throw e}return yield new Promise(e=>setTimeout(e,s)),r.headers["svix-retry-count"]=o.toString(),yield e(t,r,--i,2*s,++o)})}(t,{method:this.method.toString(),body:this.body,headers:Object.assign({accept:"application/json, */*;q=0.8",authorization:`Bearer ${e.token}`,"user-agent":o,"svix-req-id":r.toString()},this.headerParams),credentials:i?"same-origin":void 0,signal:void 0!==e.timeout?AbortSignal.timeout(e.timeout):void 0})))})}}t.SvixRequest=n},33868:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ShopifyConfigOutSerializer=void 0,t.ShopifyConfigOutSerializer={_fromJsonObject:e=>({}),_toJsonObject:e=>({})}},33873:e=>{"use strict";e.exports=require("path")},34166:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EndpointInSerializer=void 0,t.EndpointInSerializer={_fromJsonObject:e=>({channels:e.channels,description:e.description,disabled:e.disabled,filterTypes:e.filterTypes,headers:e.headers,metadata:e.metadata,rateLimit:e.rateLimit,secret:e.secret,uid:e.uid,url:e.url,version:e.version}),_toJsonObject:e=>({channels:e.channels,description:e.description,disabled:e.disabled,filterTypes:e.filterTypes,headers:e.headers,metadata:e.metadata,rateLimit:e.rateLimit,secret:e.secret,uid:e.uid,url:e.url,version:e.version})}},34371:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ConnectorKindSerializer=t.ConnectorKind=void 0,function(e){e.Custom="Custom",e.CustomerIo="CustomerIO",e.Discord="Discord",e.Hubspot="Hubspot",e.Inngest="Inngest",e.Salesforce="Salesforce",e.Segment="Segment",e.Slack="Slack",e.Teams="Teams",e.TriggerDev="TriggerDev",e.Windmill="Windmill",e.Zapier="Zapier"}(t.ConnectorKind||(t.ConnectorKind={})),t.ConnectorKindSerializer={_fromJsonObject:e=>e,_toJsonObject:e=>e}},34631:e=>{"use strict";e.exports=require("tls")},35310:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MessageEndpointOutSerializer=void 0;let i=r(2831);t.MessageEndpointOutSerializer={_fromJsonObject:e=>({channels:e.channels,createdAt:new Date(e.createdAt),description:e.description,disabled:e.disabled,filterTypes:e.filterTypes,id:e.id,nextAttempt:e.nextAttempt?new Date(e.nextAttempt):null,rateLimit:e.rateLimit,status:i.MessageStatusSerializer._fromJsonObject(e.status),uid:e.uid,updatedAt:new Date(e.updatedAt),url:e.url,version:e.version}),_toJsonObject:e=>({channels:e.channels,createdAt:e.createdAt,description:e.description,disabled:e.disabled,filterTypes:e.filterTypes,id:e.id,nextAttempt:e.nextAttempt,rateLimit:e.rateLimit,status:i.MessageStatusSerializer._toJsonObject(e.status),uid:e.uid,updatedAt:e.updatedAt,url:e.url,version:e.version})}},35520:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.IngestEndpointUpdateSerializer=void 0,t.IngestEndpointUpdateSerializer={_fromJsonObject:e=>({description:e.description,disabled:e.disabled,metadata:e.metadata,rateLimit:e.rateLimit,uid:e.uid,url:e.url}),_toJsonObject:e=>({description:e.description,disabled:e.disabled,metadata:e.metadata,rateLimit:e.rateLimit,uid:e.uid,url:e.url})}},36162:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EventTypePatchSerializer=void 0,t.EventTypePatchSerializer={_fromJsonObject:e=>({archived:e.archived,deprecated:e.deprecated,description:e.description,featureFlag:e.featureFlag,groupName:e.groupName,schemas:e.schemas}),_toJsonObject:e=>({archived:e.archived,deprecated:e.deprecated,description:e.description,featureFlag:e.featureFlag,groupName:e.groupName,schemas:e.schemas})}},36583:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EndpointPatchSerializer=void 0,t.EndpointPatchSerializer={_fromJsonObject:e=>({channels:e.channels,description:e.description,disabled:e.disabled,filterTypes:e.filterTypes,metadata:e.metadata,rateLimit:e.rateLimit,secret:e.secret,uid:e.uid,url:e.url,version:e.version}),_toJsonObject:e=>({channels:e.channels,description:e.description,disabled:e.disabled,filterTypes:e.filterTypes,metadata:e.metadata,rateLimit:e.rateLimit,secret:e.secret,uid:e.uid,url:e.url,version:e.version})}},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38137:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ListResponseIntegrationOutSerializer=void 0;let i=r(88554);t.ListResponseIntegrationOutSerializer={_fromJsonObject:e=>({data:e.data.map(e=>i.IntegrationOutSerializer._fromJsonObject(e)),done:e.done,iterator:e.iterator,prevIterator:e.prevIterator}),_toJsonObject:e=>({data:e.data.map(e=>i.IntegrationOutSerializer._toJsonObject(e)),done:e.done,iterator:e.iterator,prevIterator:e.prevIterator})}},38522:e=>{"use strict";e.exports=require("node:zlib")},38787:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.IntegrationInSerializer=void 0,t.IntegrationInSerializer={_fromJsonObject:e=>({featureFlags:e.featureFlags,name:e.name}),_toJsonObject:e=>({featureFlags:e.featureFlags,name:e.name})}},39396:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.IngestEndpointHeadersInSerializer=void 0,t.IngestEndpointHeadersInSerializer={_fromJsonObject:e=>({headers:e.headers}),_toJsonObject:e=>({headers:e.headers})}},39954:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.BackgroundTaskOutSerializer=void 0;let i=r(92091),a=r(14593);t.BackgroundTaskOutSerializer={_fromJsonObject:e=>({data:e.data,id:e.id,status:i.BackgroundTaskStatusSerializer._fromJsonObject(e.status),task:a.BackgroundTaskTypeSerializer._fromJsonObject(e.task)}),_toJsonObject:e=>({data:e.data,id:e.id,status:i.BackgroundTaskStatusSerializer._toJsonObject(e.status),task:a.BackgroundTaskTypeSerializer._toJsonObject(e.task)})}},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},42725:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EndpointSecretOutSerializer=void 0,t.EndpointSecretOutSerializer={_fromJsonObject:e=>({key:e.key}),_toJsonObject:e=>({key:e.key})}},43528:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SvixConfigOutSerializer=void 0,t.SvixConfigOutSerializer={_fromJsonObject:e=>({}),_toJsonObject:e=>({})}},44200:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ReplayInSerializer=void 0,t.ReplayInSerializer={_fromJsonObject:e=>({since:new Date(e.since),until:e.until?new Date(e.until):null}),_toJsonObject:e=>({since:e.since,until:e.until})}},44467:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.OperationalWebhook=void 0;let i=r(54638);class a{constructor(e){this.requestCtx=e}get endpoint(){return new i.OperationalWebhookEndpoint(this.requestCtx)}}t.OperationalWebhook=a},44708:e=>{"use strict";e.exports=require("node:https")},45262:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PollingEndpointConsumerSeekOutSerializer=void 0,t.PollingEndpointConsumerSeekOutSerializer={_fromJsonObject:e=>({iterator:e.iterator}),_toJsonObject:e=>({iterator:e.iterator})}},45365:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.IngestSourceInSerializer=void 0;let i=r(51418),a=r(45538),s=r(27070),o=r(76937),n=r(21439),d=r(48029),p=r(96762),u=r(62284),c=r(13535),l=r(65494),m=r(45965);t.IngestSourceInSerializer={_fromJsonObject(e){let t,r=e.type;switch(r){case"generic-webhook":t={};break;case"cron":t=a.CronConfigSerializer._fromJsonObject(e.config);break;case"adobe-sign":t=i.AdobeSignConfigSerializer._fromJsonObject(e.config);break;case"beehiiv":case"brex":case"clerk":case"guesty":case"incident-io":case"lithic":case"nash":case"pleo":case"replicate":case"resend":case"safebase":case"sardine":case"stych":case"svix":t=l.SvixConfigSerializer._fromJsonObject(e.config);break;case"docusign":t=s.DocusignConfigSerializer._fromJsonObject(e.config);break;case"github":t=o.GithubConfigSerializer._fromJsonObject(e.config);break;case"hubspot":t=n.HubspotConfigSerializer._fromJsonObject(e.config);break;case"segment":t=d.SegmentConfigSerializer._fromJsonObject(e.config);break;case"shopify":t=p.ShopifyConfigSerializer._fromJsonObject(e.config);break;case"slack":t=u.SlackConfigSerializer._fromJsonObject(e.config);break;case"stripe":t=c.StripeConfigSerializer._fromJsonObject(e.config);break;case"zoom":t=m.ZoomConfigSerializer._fromJsonObject(e.config)}return{type:r,config:t,name:e.name,uid:e.uid}},_toJsonObject(e){let t;switch(e.type){case"generic-webhook":t={};break;case"cron":t=a.CronConfigSerializer._toJsonObject(e.config);break;case"adobe-sign":t=i.AdobeSignConfigSerializer._toJsonObject(e.config);break;case"beehiiv":case"brex":case"clerk":case"guesty":case"incident-io":case"lithic":case"nash":case"pleo":case"replicate":case"resend":case"safebase":case"sardine":case"stych":case"svix":t=l.SvixConfigSerializer._toJsonObject(e.config);break;case"docusign":t=s.DocusignConfigSerializer._toJsonObject(e.config);break;case"github":t=o.GithubConfigSerializer._toJsonObject(e.config);break;case"hubspot":t=n.HubspotConfigSerializer._toJsonObject(e.config);break;case"segment":t=d.SegmentConfigSerializer._toJsonObject(e.config);break;case"shopify":t=p.ShopifyConfigSerializer._toJsonObject(e.config);break;case"slack":t=u.SlackConfigSerializer._toJsonObject(e.config);break;case"stripe":t=c.StripeConfigSerializer._toJsonObject(e.config);break;case"zoom":t=m.ZoomConfigSerializer._toJsonObject(e.config)}return{type:e.type,config:t,name:e.name,uid:e.uid}}}},45538:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CronConfigSerializer=void 0,t.CronConfigSerializer={_fromJsonObject:e=>({contentType:e.contentType,payload:e.payload,schedule:e.schedule}),_toJsonObject:e=>({contentType:e.contentType,payload:e.payload,schedule:e.schedule})}},45959:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ListResponseEndpointMessageOutSerializer=void 0;let i=r(6676);t.ListResponseEndpointMessageOutSerializer={_fromJsonObject:e=>({data:e.data.map(e=>i.EndpointMessageOutSerializer._fromJsonObject(e)),done:e.done,iterator:e.iterator,prevIterator:e.prevIterator}),_toJsonObject:e=>({data:e.data.map(e=>i.EndpointMessageOutSerializer._toJsonObject(e)),done:e.done,iterator:e.iterator,prevIterator:e.prevIterator})}},45965:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ZoomConfigSerializer=void 0,t.ZoomConfigSerializer={_fromJsonObject:e=>({secret:e.secret}),_toJsonObject:e=>({secret:e.secret})}},47278:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MessageAttempt=void 0;let i=r(45959),a=r(71635),s=r(90521),o=r(98086),n=r(33850);class d{constructor(e){this.requestCtx=e}listByEndpoint(e,t,r){let i=new n.SvixRequest(n.HttpMethod.GET,"/api/v1/app/{app_id}/attempt/endpoint/{endpoint_id}");return i.setPathParam("app_id",e),i.setPathParam("endpoint_id",t),i.setQueryParam("limit",null==r?void 0:r.limit),i.setQueryParam("iterator",null==r?void 0:r.iterator),i.setQueryParam("status",null==r?void 0:r.status),i.setQueryParam("status_code_class",null==r?void 0:r.statusCodeClass),i.setQueryParam("channel",null==r?void 0:r.channel),i.setQueryParam("tag",null==r?void 0:r.tag),i.setQueryParam("before",null==r?void 0:r.before),i.setQueryParam("after",null==r?void 0:r.after),i.setQueryParam("with_content",null==r?void 0:r.withContent),i.setQueryParam("with_msg",null==r?void 0:r.withMsg),i.setQueryParam("event_types",null==r?void 0:r.eventTypes),i.send(this.requestCtx,a.ListResponseMessageAttemptOutSerializer._fromJsonObject)}listByMsg(e,t,r){let i=new n.SvixRequest(n.HttpMethod.GET,"/api/v1/app/{app_id}/attempt/msg/{msg_id}");return i.setPathParam("app_id",e),i.setPathParam("msg_id",t),i.setQueryParam("limit",null==r?void 0:r.limit),i.setQueryParam("iterator",null==r?void 0:r.iterator),i.setQueryParam("status",null==r?void 0:r.status),i.setQueryParam("status_code_class",null==r?void 0:r.statusCodeClass),i.setQueryParam("channel",null==r?void 0:r.channel),i.setQueryParam("tag",null==r?void 0:r.tag),i.setQueryParam("endpoint_id",null==r?void 0:r.endpointId),i.setQueryParam("before",null==r?void 0:r.before),i.setQueryParam("after",null==r?void 0:r.after),i.setQueryParam("with_content",null==r?void 0:r.withContent),i.setQueryParam("event_types",null==r?void 0:r.eventTypes),i.send(this.requestCtx,a.ListResponseMessageAttemptOutSerializer._fromJsonObject)}listAttemptedMessages(e,t,r){let a=new n.SvixRequest(n.HttpMethod.GET,"/api/v1/app/{app_id}/endpoint/{endpoint_id}/msg");return a.setPathParam("app_id",e),a.setPathParam("endpoint_id",t),a.setQueryParam("limit",null==r?void 0:r.limit),a.setQueryParam("iterator",null==r?void 0:r.iterator),a.setQueryParam("channel",null==r?void 0:r.channel),a.setQueryParam("tag",null==r?void 0:r.tag),a.setQueryParam("status",null==r?void 0:r.status),a.setQueryParam("before",null==r?void 0:r.before),a.setQueryParam("after",null==r?void 0:r.after),a.setQueryParam("with_content",null==r?void 0:r.withContent),a.setQueryParam("event_types",null==r?void 0:r.eventTypes),a.send(this.requestCtx,i.ListResponseEndpointMessageOutSerializer._fromJsonObject)}get(e,t,r){let i=new n.SvixRequest(n.HttpMethod.GET,"/api/v1/app/{app_id}/msg/{msg_id}/attempt/{attempt_id}");return i.setPathParam("app_id",e),i.setPathParam("msg_id",t),i.setPathParam("attempt_id",r),i.send(this.requestCtx,o.MessageAttemptOutSerializer._fromJsonObject)}expungeContent(e,t,r){let i=new n.SvixRequest(n.HttpMethod.DELETE,"/api/v1/app/{app_id}/msg/{msg_id}/attempt/{attempt_id}/content");return i.setPathParam("app_id",e),i.setPathParam("msg_id",t),i.setPathParam("attempt_id",r),i.sendNoResponseBody(this.requestCtx)}listAttemptedDestinations(e,t,r){let i=new n.SvixRequest(n.HttpMethod.GET,"/api/v1/app/{app_id}/msg/{msg_id}/endpoint");return i.setPathParam("app_id",e),i.setPathParam("msg_id",t),i.setQueryParam("limit",null==r?void 0:r.limit),i.setQueryParam("iterator",null==r?void 0:r.iterator),i.send(this.requestCtx,s.ListResponseMessageEndpointOutSerializer._fromJsonObject)}resend(e,t,r,i){let a=new n.SvixRequest(n.HttpMethod.POST,"/api/v1/app/{app_id}/msg/{msg_id}/endpoint/{endpoint_id}/resend");return a.setPathParam("app_id",e),a.setPathParam("msg_id",t),a.setPathParam("endpoint_id",r),a.setHeaderParam("idempotency-key",null==i?void 0:i.idempotencyKey),a.sendNoResponseBody(this.requestCtx)}}t.MessageAttempt=d},47512:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EndpointHeadersInSerializer=void 0,t.EndpointHeadersInSerializer={_fromJsonObject:e=>({headers:e.headers}),_toJsonObject:e=>({headers:e.headers})}},48029:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SegmentConfigSerializer=void 0,t.SegmentConfigSerializer={_fromJsonObject:e=>({secret:e.secret}),_toJsonObject:e=>({secret:e.secret})}},48105:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.IngestSourceOutSerializer=void 0;let i=r(10764),a=r(45538),s=r(30704),o=r(9677),n=r(49635),d=r(545),p=r(33868),u=r(30278),c=r(48899),l=r(43528),m=r(19921);t.IngestSourceOutSerializer={_fromJsonObject(e){let t,r=e.type;switch(r){case"generic-webhook":t={};break;case"cron":t=a.CronConfigSerializer._fromJsonObject(e.config);break;case"adobe-sign":t=i.AdobeSignConfigOutSerializer._fromJsonObject(e.config);break;case"beehiiv":case"brex":case"clerk":case"guesty":case"incident-io":case"lithic":case"nash":case"pleo":case"replicate":case"resend":case"safebase":case"sardine":case"stych":case"svix":t=l.SvixConfigOutSerializer._fromJsonObject(e.config);break;case"docusign":t=s.DocusignConfigOutSerializer._fromJsonObject(e.config);break;case"github":t=o.GithubConfigOutSerializer._fromJsonObject(e.config);break;case"hubspot":t=n.HubspotConfigOutSerializer._fromJsonObject(e.config);break;case"segment":t=d.SegmentConfigOutSerializer._fromJsonObject(e.config);break;case"shopify":t=p.ShopifyConfigOutSerializer._fromJsonObject(e.config);break;case"slack":t=u.SlackConfigOutSerializer._fromJsonObject(e.config);break;case"stripe":t=c.StripeConfigOutSerializer._fromJsonObject(e.config);break;case"zoom":t=m.ZoomConfigOutSerializer._fromJsonObject(e.config)}return{type:r,config:t,createdAt:new Date(e.createdAt),id:e.id,ingestUrl:e.ingestUrl,name:e.name,uid:e.uid,updatedAt:new Date(e.updatedAt)}},_toJsonObject(e){let t;switch(e.type){case"generic-webhook":t={};break;case"cron":t=a.CronConfigSerializer._toJsonObject(e.config);break;case"adobe-sign":t=i.AdobeSignConfigOutSerializer._toJsonObject(e.config);break;case"beehiiv":case"brex":case"clerk":case"guesty":case"incident-io":case"lithic":case"nash":case"pleo":case"replicate":case"resend":case"safebase":case"sardine":case"stych":case"svix":t=l.SvixConfigOutSerializer._toJsonObject(e.config);break;case"docusign":t=s.DocusignConfigOutSerializer._toJsonObject(e.config);break;case"github":t=o.GithubConfigOutSerializer._toJsonObject(e.config);break;case"hubspot":t=n.HubspotConfigOutSerializer._toJsonObject(e.config);break;case"segment":t=d.SegmentConfigOutSerializer._toJsonObject(e.config);break;case"shopify":t=p.ShopifyConfigOutSerializer._toJsonObject(e.config);break;case"slack":t=u.SlackConfigOutSerializer._toJsonObject(e.config);break;case"stripe":t=c.StripeConfigOutSerializer._toJsonObject(e.config);break;case"zoom":t=m.ZoomConfigOutSerializer._toJsonObject(e.config)}return{type:e.type,config:t,createdAt:e.createdAt,id:e.id,ingestUrl:e.ingestUrl,name:e.name,uid:e.uid,updatedAt:e.updatedAt}}}},48161:e=>{"use strict";e.exports=require("node:os")},48899:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.StripeConfigOutSerializer=void 0,t.StripeConfigOutSerializer={_fromJsonObject:e=>({}),_toJsonObject:e=>({})}},49353:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.IngestEndpointOutSerializer=void 0,t.IngestEndpointOutSerializer={_fromJsonObject:e=>({createdAt:new Date(e.createdAt),description:e.description,disabled:e.disabled,id:e.id,metadata:e.metadata,rateLimit:e.rateLimit,uid:e.uid,updatedAt:new Date(e.updatedAt),url:e.url}),_toJsonObject:e=>({createdAt:e.createdAt,description:e.description,disabled:e.disabled,id:e.id,metadata:e.metadata,rateLimit:e.rateLimit,uid:e.uid,updatedAt:e.updatedAt,url:e.url})}},49635:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.HubspotConfigOutSerializer=void 0,t.HubspotConfigOutSerializer={_fromJsonObject:e=>({}),_toJsonObject:e=>({})}},51418:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AdobeSignConfigSerializer=void 0,t.AdobeSignConfigSerializer={_fromJsonObject:e=>({clientId:e.clientId}),_toJsonObject:e=>({clientId:e.clientId})}},51690:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EndpointStatsSerializer=void 0,t.EndpointStatsSerializer={_fromJsonObject:e=>({fail:e.fail,pending:e.pending,sending:e.sending,success:e.success}),_toJsonObject:e=>({fail:e.fail,pending:e.pending,sending:e.sending,success:e.success})}},52118:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EndpointTransformationOutSerializer=void 0,t.EndpointTransformationOutSerializer={_fromJsonObject:e=>({code:e.code,enabled:e.enabled}),_toJsonObject:e=>({code:e.code,enabled:e.enabled})}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},53698:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.IngestEndpointInSerializer=void 0,t.IngestEndpointInSerializer={_fromJsonObject:e=>({description:e.description,disabled:e.disabled,metadata:e.metadata,rateLimit:e.rateLimit,secret:e.secret,uid:e.uid,url:e.url}),_toJsonObject:e=>({description:e.description,disabled:e.disabled,metadata:e.metadata,rateLimit:e.rateLimit,secret:e.secret,uid:e.uid,url:e.url})}},54287:e=>{"use strict";e.exports=require("console")},54638:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.OperationalWebhookEndpoint=void 0;let i=r(66451),a=r(10755),s=r(17130),o=r(83043),n=r(76938),d=r(79819),p=r(66386),u=r(16141),c=r(33850);class l{constructor(e){this.requestCtx=e}list(e){let t=new c.SvixRequest(c.HttpMethod.GET,"/api/v1/operational-webhook/endpoint");return t.setQueryParam("limit",null==e?void 0:e.limit),t.setQueryParam("iterator",null==e?void 0:e.iterator),t.setQueryParam("order",null==e?void 0:e.order),t.send(this.requestCtx,i.ListResponseOperationalWebhookEndpointOutSerializer._fromJsonObject)}create(e,t){let r=new c.SvixRequest(c.HttpMethod.POST,"/api/v1/operational-webhook/endpoint");return r.setHeaderParam("idempotency-key",null==t?void 0:t.idempotencyKey),r.setBody(o.OperationalWebhookEndpointInSerializer._toJsonObject(e)),r.send(this.requestCtx,n.OperationalWebhookEndpointOutSerializer._fromJsonObject)}get(e){let t=new c.SvixRequest(c.HttpMethod.GET,"/api/v1/operational-webhook/endpoint/{endpoint_id}");return t.setPathParam("endpoint_id",e),t.send(this.requestCtx,n.OperationalWebhookEndpointOutSerializer._fromJsonObject)}update(e,t){let r=new c.SvixRequest(c.HttpMethod.PUT,"/api/v1/operational-webhook/endpoint/{endpoint_id}");return r.setPathParam("endpoint_id",e),r.setBody(u.OperationalWebhookEndpointUpdateSerializer._toJsonObject(t)),r.send(this.requestCtx,n.OperationalWebhookEndpointOutSerializer._fromJsonObject)}delete(e){let t=new c.SvixRequest(c.HttpMethod.DELETE,"/api/v1/operational-webhook/endpoint/{endpoint_id}");return t.setPathParam("endpoint_id",e),t.sendNoResponseBody(this.requestCtx)}getHeaders(e){let t=new c.SvixRequest(c.HttpMethod.GET,"/api/v1/operational-webhook/endpoint/{endpoint_id}/headers");return t.setPathParam("endpoint_id",e),t.send(this.requestCtx,s.OperationalWebhookEndpointHeadersOutSerializer._fromJsonObject)}updateHeaders(e,t){let r=new c.SvixRequest(c.HttpMethod.PUT,"/api/v1/operational-webhook/endpoint/{endpoint_id}/headers");return r.setPathParam("endpoint_id",e),r.setBody(a.OperationalWebhookEndpointHeadersInSerializer._toJsonObject(t)),r.sendNoResponseBody(this.requestCtx)}getSecret(e){let t=new c.SvixRequest(c.HttpMethod.GET,"/api/v1/operational-webhook/endpoint/{endpoint_id}/secret");return t.setPathParam("endpoint_id",e),t.send(this.requestCtx,p.OperationalWebhookEndpointSecretOutSerializer._fromJsonObject)}rotateSecret(e,t,r){let i=new c.SvixRequest(c.HttpMethod.POST,"/api/v1/operational-webhook/endpoint/{endpoint_id}/secret/rotate");return i.setPathParam("endpoint_id",e),i.setHeaderParam("idempotency-key",null==r?void 0:r.idempotencyKey),i.setBody(d.OperationalWebhookEndpointSecretInSerializer._toJsonObject(t)),i.sendNoResponseBody(this.requestCtx)}}t.OperationalWebhookEndpoint=l},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56420:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.RotateTokenOutSerializer=void 0,t.RotateTokenOutSerializer={_fromJsonObject:e=>({ingestUrl:e.ingestUrl}),_toJsonObject:e=>({ingestUrl:e.ingestUrl})}},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57205:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ApiTokenOutSerializer=void 0,t.ApiTokenOutSerializer={_fromJsonObject:e=>({createdAt:new Date(e.createdAt),expiresAt:e.expiresAt?new Date(e.expiresAt):null,id:e.id,name:e.name,scopes:e.scopes,token:e.token}),_toJsonObject:e=>({createdAt:e.createdAt,expiresAt:e.expiresAt,id:e.id,name:e.name,scopes:e.scopes,token:e.token})}},57975:e=>{"use strict";e.exports=require("node:util")},59988:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"7fa248ee4cee001992d543e3927d536ddea63c121c":()=>i.ai,"7fd10f19b29f8e8b2951e0bb60d9466e540ba24937":()=>i.ot,"7fe80fb1c9bdcbbae5a7da0586440a249dd4fb207a":()=>a.y,"7ffdf714159b7e9cad55b4d3d168d12a9fa0cc1b9f":()=>i.at});var i=r(54841),a=r(44089)},60528:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ListResponseEndpointOutSerializer=void 0;let i=r(4053);t.ListResponseEndpointOutSerializer={_fromJsonObject:e=>({data:e.data.map(e=>i.EndpointOutSerializer._fromJsonObject(e)),done:e.done,iterator:e.iterator,prevIterator:e.prevIterator}),_toJsonObject:e=>({data:e.data.map(e=>i.EndpointOutSerializer._toJsonObject(e)),done:e.done,iterator:e.iterator,prevIterator:e.prevIterator})}},60854:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MessageAttemptTriggerTypeSerializer=t.MessageAttemptTriggerType=void 0,function(e){e[e.Scheduled=0]="Scheduled",e[e.Manual=1]="Manual"}(t.MessageAttemptTriggerType||(t.MessageAttemptTriggerType={})),t.MessageAttemptTriggerTypeSerializer={_fromJsonObject:e=>e,_toJsonObject:e=>e}},62284:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SlackConfigSerializer=void 0,t.SlackConfigSerializer={_fromJsonObject:e=>({secret:e.secret}),_toJsonObject:e=>({secret:e.secret})}},62308:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EventTypeFromOpenApiSerializer=void 0,t.EventTypeFromOpenApiSerializer={_fromJsonObject:e=>({deprecated:e.deprecated,description:e.description,featureFlag:e.featureFlag,groupName:e.groupName,name:e.name,schemas:e.schemas}),_toJsonObject:e=>({deprecated:e.deprecated,description:e.description,featureFlag:e.featureFlag,groupName:e.groupName,name:e.name,schemas:e.schemas})}},62315:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.IngestEndpoint=void 0;let i=r(39396),a=r(67663),s=r(53698),o=r(49353),n=r(24458),d=r(89009),p=r(35520),u=r(16456),c=r(33850);class l{constructor(e){this.requestCtx=e}list(e,t){let r=new c.SvixRequest(c.HttpMethod.GET,"/ingest/api/v1/source/{source_id}/endpoint");return r.setPathParam("source_id",e),r.setQueryParam("limit",null==t?void 0:t.limit),r.setQueryParam("iterator",null==t?void 0:t.iterator),r.setQueryParam("order",null==t?void 0:t.order),r.send(this.requestCtx,u.ListResponseIngestEndpointOutSerializer._fromJsonObject)}create(e,t,r){let i=new c.SvixRequest(c.HttpMethod.POST,"/ingest/api/v1/source/{source_id}/endpoint");return i.setPathParam("source_id",e),i.setHeaderParam("idempotency-key",null==r?void 0:r.idempotencyKey),i.setBody(s.IngestEndpointInSerializer._toJsonObject(t)),i.send(this.requestCtx,o.IngestEndpointOutSerializer._fromJsonObject)}get(e,t){let r=new c.SvixRequest(c.HttpMethod.GET,"/ingest/api/v1/source/{source_id}/endpoint/{endpoint_id}");return r.setPathParam("source_id",e),r.setPathParam("endpoint_id",t),r.send(this.requestCtx,o.IngestEndpointOutSerializer._fromJsonObject)}update(e,t,r){let i=new c.SvixRequest(c.HttpMethod.PUT,"/ingest/api/v1/source/{source_id}/endpoint/{endpoint_id}");return i.setPathParam("source_id",e),i.setPathParam("endpoint_id",t),i.setBody(p.IngestEndpointUpdateSerializer._toJsonObject(r)),i.send(this.requestCtx,o.IngestEndpointOutSerializer._fromJsonObject)}delete(e,t){let r=new c.SvixRequest(c.HttpMethod.DELETE,"/ingest/api/v1/source/{source_id}/endpoint/{endpoint_id}");return r.setPathParam("source_id",e),r.setPathParam("endpoint_id",t),r.sendNoResponseBody(this.requestCtx)}getHeaders(e,t){let r=new c.SvixRequest(c.HttpMethod.GET,"/ingest/api/v1/source/{source_id}/endpoint/{endpoint_id}/headers");return r.setPathParam("source_id",e),r.setPathParam("endpoint_id",t),r.send(this.requestCtx,a.IngestEndpointHeadersOutSerializer._fromJsonObject)}updateHeaders(e,t,r){let a=new c.SvixRequest(c.HttpMethod.PUT,"/ingest/api/v1/source/{source_id}/endpoint/{endpoint_id}/headers");return a.setPathParam("source_id",e),a.setPathParam("endpoint_id",t),a.setBody(i.IngestEndpointHeadersInSerializer._toJsonObject(r)),a.sendNoResponseBody(this.requestCtx)}getSecret(e,t){let r=new c.SvixRequest(c.HttpMethod.GET,"/ingest/api/v1/source/{source_id}/endpoint/{endpoint_id}/secret");return r.setPathParam("source_id",e),r.setPathParam("endpoint_id",t),r.send(this.requestCtx,d.IngestEndpointSecretOutSerializer._fromJsonObject)}rotateSecret(e,t,r,i){let a=new c.SvixRequest(c.HttpMethod.POST,"/ingest/api/v1/source/{source_id}/endpoint/{endpoint_id}/secret/rotate");return a.setPathParam("source_id",e),a.setPathParam("endpoint_id",t),a.setHeaderParam("idempotency-key",null==i?void 0:i.idempotencyKey),a.setBody(n.IngestEndpointSecretInSerializer._toJsonObject(r)),a.sendNoResponseBody(this.requestCtx)}}t.IngestEndpoint=l},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63094:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Application=void 0;let i=r(22235),a=r(22434),s=r(92764),o=r(74173),n=r(33850);class d{constructor(e){this.requestCtx=e}list(e){let t=new n.SvixRequest(n.HttpMethod.GET,"/api/v1/app");return t.setQueryParam("limit",null==e?void 0:e.limit),t.setQueryParam("iterator",null==e?void 0:e.iterator),t.setQueryParam("order",null==e?void 0:e.order),t.send(this.requestCtx,o.ListResponseApplicationOutSerializer._fromJsonObject)}create(e,t){let r=new n.SvixRequest(n.HttpMethod.POST,"/api/v1/app");return r.setHeaderParam("idempotency-key",null==t?void 0:t.idempotencyKey),r.setBody(i.ApplicationInSerializer._toJsonObject(e)),r.send(this.requestCtx,a.ApplicationOutSerializer._fromJsonObject)}getOrCreate(e,t){let r=new n.SvixRequest(n.HttpMethod.POST,"/api/v1/app");return r.setQueryParam("get_if_exists",!0),r.setHeaderParam("idempotency-key",null==t?void 0:t.idempotencyKey),r.setBody(i.ApplicationInSerializer._toJsonObject(e)),r.send(this.requestCtx,a.ApplicationOutSerializer._fromJsonObject)}get(e){let t=new n.SvixRequest(n.HttpMethod.GET,"/api/v1/app/{app_id}");return t.setPathParam("app_id",e),t.send(this.requestCtx,a.ApplicationOutSerializer._fromJsonObject)}update(e,t){let r=new n.SvixRequest(n.HttpMethod.PUT,"/api/v1/app/{app_id}");return r.setPathParam("app_id",e),r.setBody(i.ApplicationInSerializer._toJsonObject(t)),r.send(this.requestCtx,a.ApplicationOutSerializer._fromJsonObject)}delete(e){let t=new n.SvixRequest(n.HttpMethod.DELETE,"/api/v1/app/{app_id}");return t.setPathParam("app_id",e),t.sendNoResponseBody(this.requestCtx)}patch(e,t){let r=new n.SvixRequest(n.HttpMethod.PATCH,"/api/v1/app/{app_id}");return r.setPathParam("app_id",e),r.setBody(s.ApplicationPatchSerializer._toJsonObject(t)),r.send(this.requestCtx,a.ApplicationOutSerializer._fromJsonObject)}}t.Application=d},63873:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Statistics=void 0;let i=r(69398),a=r(70996),s=r(22783),o=r(33850);class n{constructor(e){this.requestCtx=e}aggregateAppStats(e,t){let r=new o.SvixRequest(o.HttpMethod.POST,"/api/v1/stats/usage/app");return r.setHeaderParam("idempotency-key",null==t?void 0:t.idempotencyKey),r.setBody(a.AppUsageStatsInSerializer._toJsonObject(e)),r.send(this.requestCtx,s.AppUsageStatsOutSerializer._fromJsonObject)}aggregateEventTypes(){return new o.SvixRequest(o.HttpMethod.PUT,"/api/v1/stats/usage/event-types").send(this.requestCtx,i.AggregateEventTypesOutSerializer._fromJsonObject)}}t.Statistics=n},64624:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ListResponseBackgroundTaskOutSerializer=void 0;let i=r(39954);t.ListResponseBackgroundTaskOutSerializer={_fromJsonObject:e=>({data:e.data.map(e=>i.BackgroundTaskOutSerializer._fromJsonObject(e)),done:e.done,iterator:e.iterator,prevIterator:e.prevIterator}),_toJsonObject:e=>({data:e.data.map(e=>i.BackgroundTaskOutSerializer._toJsonObject(e)),done:e.done,iterator:e.iterator,prevIterator:e.prevIterator})}},65038:(e,t)=>{"use strict";function r(e,t=""){if(!e)throw Error(t)}Object.defineProperty(t,"__esModule",{value:!0}),t.timingSafeEqual=void 0,t.timingSafeEqual=function(e,t){if(e.byteLength!==t.byteLength)return!1;e instanceof DataView||(e=new DataView(ArrayBuffer.isView(e)?e.buffer:e)),t instanceof DataView||(t=new DataView(ArrayBuffer.isView(t)?t.buffer:t)),r(e instanceof DataView),r(t instanceof DataView);let i=e.byteLength,a=0,s=-1;for(;++s<i;)a|=e.getUint8(s)^t.getUint8(s);return 0===a}},65494:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SvixConfigSerializer=void 0,t.SvixConfigSerializer={_fromJsonObject:e=>({secret:e.secret}),_toJsonObject:e=>({secret:e.secret})}},65593:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EndpointDisabledTriggerSerializer=t.EndpointDisabledTrigger=void 0,function(e){e.Manual="manual",e.Automatic="automatic"}(t.EndpointDisabledTrigger||(t.EndpointDisabledTrigger={})),t.EndpointDisabledTriggerSerializer={_fromJsonObject:e=>e,_toJsonObject:e=>e}},66386:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.OperationalWebhookEndpointSecretOutSerializer=void 0,t.OperationalWebhookEndpointSecretOutSerializer={_fromJsonObject:e=>({key:e.key}),_toJsonObject:e=>({key:e.key})}},66451:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ListResponseOperationalWebhookEndpointOutSerializer=void 0;let i=r(76938);t.ListResponseOperationalWebhookEndpointOutSerializer={_fromJsonObject:e=>({data:e.data.map(e=>i.OperationalWebhookEndpointOutSerializer._fromJsonObject(e)),done:e.done,iterator:e.iterator,prevIterator:e.prevIterator}),_toJsonObject:e=>({data:e.data.map(e=>i.OperationalWebhookEndpointOutSerializer._toJsonObject(e)),done:e.done,iterator:e.iterator,prevIterator:e.prevIterator})}},67195:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EndpointHeadersOutSerializer=void 0,t.EndpointHeadersOutSerializer={_fromJsonObject:e=>({headers:e.headers,sensitive:e.sensitive}),_toJsonObject:e=>({headers:e.headers,sensitive:e.sensitive})}},67663:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.IngestEndpointHeadersOutSerializer=void 0,t.IngestEndpointHeadersOutSerializer={_fromJsonObject:e=>({headers:e.headers,sensitive:e.sensitive}),_toJsonObject:e=>({headers:e.headers,sensitive:e.sensitive})}},69342:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.StatusCodeClass=t.Ordering=t.MessageStatus=t.MessageAttemptTriggerType=t.EndpointDisabledTrigger=t.ConnectorKind=t.BackgroundTaskType=t.BackgroundTaskStatus=void 0;var i=r(92091);Object.defineProperty(t,"BackgroundTaskStatus",{enumerable:!0,get:function(){return i.BackgroundTaskStatus}});var a=r(14593);Object.defineProperty(t,"BackgroundTaskType",{enumerable:!0,get:function(){return a.BackgroundTaskType}});var s=r(34371);Object.defineProperty(t,"ConnectorKind",{enumerable:!0,get:function(){return s.ConnectorKind}});var o=r(65593);Object.defineProperty(t,"EndpointDisabledTrigger",{enumerable:!0,get:function(){return o.EndpointDisabledTrigger}});var n=r(60854);Object.defineProperty(t,"MessageAttemptTriggerType",{enumerable:!0,get:function(){return n.MessageAttemptTriggerType}});var d=r(2831);Object.defineProperty(t,"MessageStatus",{enumerable:!0,get:function(){return d.MessageStatus}});var p=r(10512);Object.defineProperty(t,"Ordering",{enumerable:!0,get:function(){return p.Ordering}});var u=r(17895);Object.defineProperty(t,"StatusCodeClass",{enumerable:!0,get:function(){return u.StatusCodeClass}})},69398:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AggregateEventTypesOutSerializer=void 0;let i=r(92091),a=r(14593);t.AggregateEventTypesOutSerializer={_fromJsonObject:e=>({id:e.id,status:i.BackgroundTaskStatusSerializer._fromJsonObject(e.status),task:a.BackgroundTaskTypeSerializer._fromJsonObject(e.task)}),_toJsonObject:e=>({id:e.id,status:i.BackgroundTaskStatusSerializer._toJsonObject(e.status),task:a.BackgroundTaskTypeSerializer._toJsonObject(e.task)})}},69682:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DashboardAccessOutSerializer=void 0,t.DashboardAccessOutSerializer={_fromJsonObject:e=>({token:e.token,url:e.url}),_toJsonObject:e=>({token:e.token,url:e.url})}},70903:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ApplicationTokenExpireInSerializer=void 0,t.ApplicationTokenExpireInSerializer={_fromJsonObject:e=>({expiry:e.expiry}),_toJsonObject:e=>({expiry:e.expiry})}},70996:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AppUsageStatsInSerializer=void 0,t.AppUsageStatsInSerializer={_fromJsonObject:e=>({appIds:e.appIds,since:new Date(e.since),until:new Date(e.until)}),_toJsonObject:e=>({appIds:e.appIds,since:e.since,until:e.until})}},71635:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ListResponseMessageAttemptOutSerializer=void 0;let i=r(98086);t.ListResponseMessageAttemptOutSerializer={_fromJsonObject:e=>({data:e.data.map(e=>i.MessageAttemptOutSerializer._fromJsonObject(e)),done:e.done,iterator:e.iterator,prevIterator:e.prevIterator}),_toJsonObject:e=>({data:e.data.map(e=>i.MessageAttemptOutSerializer._toJsonObject(e)),done:e.done,iterator:e.iterator,prevIterator:e.prevIterator})}},72235:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ReplayOutSerializer=void 0;let i=r(92091),a=r(14593);t.ReplayOutSerializer={_fromJsonObject:e=>({id:e.id,status:i.BackgroundTaskStatusSerializer._fromJsonObject(e.status),task:a.BackgroundTaskTypeSerializer._fromJsonObject(e.task)}),_toJsonObject:e=>({id:e.id,status:i.BackgroundTaskStatusSerializer._toJsonObject(e.status),task:a.BackgroundTaskTypeSerializer._toJsonObject(e.task)})}},72565:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ExpungeAllContentsOutSerializer=void 0;let i=r(92091),a=r(14593);t.ExpungeAllContentsOutSerializer={_fromJsonObject:e=>({id:e.id,status:i.BackgroundTaskStatusSerializer._fromJsonObject(e.status),task:a.BackgroundTaskTypeSerializer._fromJsonObject(e.task)}),_toJsonObject:e=>({id:e.id,status:i.BackgroundTaskStatusSerializer._toJsonObject(e.status),task:a.BackgroundTaskTypeSerializer._toJsonObject(e.task)})}},73001:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.IntegrationKeyOutSerializer=void 0,t.IntegrationKeyOutSerializer={_fromJsonObject:e=>({key:e.key}),_toJsonObject:e=>({key:e.key})}},73024:e=>{"use strict";e.exports=require("node:fs")},73455:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EventTypeImportOpenApiOutSerializer=void 0;let i=r(9927);t.EventTypeImportOpenApiOutSerializer={_fromJsonObject:e=>({data:i.EventTypeImportOpenApiOutDataSerializer._fromJsonObject(e.data)}),_toJsonObject:e=>({data:i.EventTypeImportOpenApiOutDataSerializer._toJsonObject(e.data)})}},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74173:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ListResponseApplicationOutSerializer=void 0;let i=r(22434);t.ListResponseApplicationOutSerializer={_fromJsonObject:e=>({data:e.data.map(e=>i.ApplicationOutSerializer._fromJsonObject(e)),done:e.done,iterator:e.iterator,prevIterator:e.prevIterator}),_toJsonObject:e=>({data:e.data.map(e=>i.ApplicationOutSerializer._toJsonObject(e)),done:e.done,iterator:e.iterator,prevIterator:e.prevIterator})}},74378:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.IngestSourceConsumerPortalAccessInSerializer=void 0,t.IngestSourceConsumerPortalAccessInSerializer={_fromJsonObject:e=>({expiry:e.expiry,readOnly:e.readOnly}),_toJsonObject:e=>({expiry:e.expiry,readOnly:e.readOnly})}},74788:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ListResponseIngestSourceOutSerializer=void 0;let i=r(48105);t.ListResponseIngestSourceOutSerializer={_fromJsonObject:e=>({data:e.data.map(e=>i.IngestSourceOutSerializer._fromJsonObject(e)),done:e.done,iterator:e.iterator,prevIterator:e.prevIterator}),_toJsonObject:e=>({data:e.data.map(e=>i.IngestSourceOutSerializer._toJsonObject(e)),done:e.done,iterator:e.iterator,prevIterator:e.prevIterator})}},74967:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,metadata:()=>p});var i=r(94752),a=r(37838),s=r(17211);let o=(0,r(12238).H)().SVIX_TOKEN,n={getAppPortal:async()=>{if(!o)throw Error("SVIX_TOKEN is not set");let e=new s.Svix(o),{orgId:t}=await (0,a.j)();if(t)return e.authentication.appPortalAccess(t,{application:{name:t,uid:t}})}};var d=r(62923);let p={title:"Webhooks",description:"Send webhooks to your users."},u=async()=>{let e=await n.getAppPortal();return e?.url||(0,d.notFound)(),(0,i.jsx)("div",{className:"h-full w-full overflow-hidden",children:(0,i.jsx)("iframe",{title:"Webhooks",src:e.url,className:"h-full w-full border-none",allow:"clipboard-write",loading:"lazy"})})}},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76546:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Authentication=void 0;let i=r(33398),a=r(89877),s=r(70903),o=r(69682),n=r(33850);class d{constructor(e){this.requestCtx=e}appPortalAccess(e,t,r){let s=new n.SvixRequest(n.HttpMethod.POST,"/api/v1/auth/app-portal-access/{app_id}");return s.setPathParam("app_id",e),s.setHeaderParam("idempotency-key",null==r?void 0:r.idempotencyKey),s.setBody(i.AppPortalAccessInSerializer._toJsonObject(t)),s.send(this.requestCtx,a.AppPortalAccessOutSerializer._fromJsonObject)}expireAll(e,t,r){let i=new n.SvixRequest(n.HttpMethod.POST,"/api/v1/auth/app/{app_id}/expire-all");return i.setPathParam("app_id",e),i.setHeaderParam("idempotency-key",null==r?void 0:r.idempotencyKey),i.setBody(s.ApplicationTokenExpireInSerializer._toJsonObject(t)),i.sendNoResponseBody(this.requestCtx)}dashboardAccess(e,t){let r=new n.SvixRequest(n.HttpMethod.POST,"/api/v1/auth/dashboard-access/{app_id}");return r.setPathParam("app_id",e),r.setHeaderParam("idempotency-key",null==t?void 0:t.idempotencyKey),r.send(this.requestCtx,o.DashboardAccessOutSerializer._fromJsonObject)}logout(e){let t=new n.SvixRequest(n.HttpMethod.POST,"/api/v1/auth/logout");return t.setHeaderParam("idempotency-key",null==e?void 0:e.idempotencyKey),t.sendNoResponseBody(this.requestCtx)}}t.Authentication=d},76760:e=>{"use strict";e.exports=require("node:path")},76937:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.GithubConfigSerializer=void 0,t.GithubConfigSerializer={_fromJsonObject:e=>({secret:e.secret}),_toJsonObject:e=>({secret:e.secret})}},76938:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.OperationalWebhookEndpointOutSerializer=void 0,t.OperationalWebhookEndpointOutSerializer={_fromJsonObject:e=>({createdAt:new Date(e.createdAt),description:e.description,disabled:e.disabled,filterTypes:e.filterTypes,id:e.id,metadata:e.metadata,rateLimit:e.rateLimit,uid:e.uid,updatedAt:new Date(e.updatedAt),url:e.url}),_toJsonObject:e=>({createdAt:e.createdAt,description:e.description,disabled:e.disabled,filterTypes:e.filterTypes,id:e.id,metadata:e.metadata,rateLimit:e.rateLimit,uid:e.uid,updatedAt:e.updatedAt,url:e.url})}},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},78480:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EventTypeOutSerializer=void 0,t.EventTypeOutSerializer={_fromJsonObject:e=>({archived:e.archived,createdAt:new Date(e.createdAt),deprecated:e.deprecated,description:e.description,featureFlag:e.featureFlag,groupName:e.groupName,name:e.name,schemas:e.schemas,updatedAt:new Date(e.updatedAt)}),_toJsonObject:e=>({archived:e.archived,createdAt:e.createdAt,deprecated:e.deprecated,description:e.description,featureFlag:e.featureFlag,groupName:e.groupName,name:e.name,schemas:e.schemas,updatedAt:e.updatedAt})}},78889:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MessagePoller=void 0;let i=r(28287),a=r(45262),s=r(87818),o=r(33850);class n{constructor(e){this.requestCtx=e}poll(e,t,r){let i=new o.SvixRequest(o.HttpMethod.GET,"/api/v1/app/{app_id}/poller/{sink_id}");return i.setPathParam("app_id",e),i.setPathParam("sink_id",t),i.setQueryParam("limit",null==r?void 0:r.limit),i.setQueryParam("iterator",null==r?void 0:r.iterator),i.setQueryParam("event_type",null==r?void 0:r.eventType),i.setQueryParam("channel",null==r?void 0:r.channel),i.setQueryParam("after",null==r?void 0:r.after),i.send(this.requestCtx,s.PollingEndpointOutSerializer._fromJsonObject)}consumerPoll(e,t,r,i){let a=new o.SvixRequest(o.HttpMethod.GET,"/api/v1/app/{app_id}/poller/{sink_id}/consumer/{consumer_id}");return a.setPathParam("app_id",e),a.setPathParam("sink_id",t),a.setPathParam("consumer_id",r),a.setQueryParam("limit",null==i?void 0:i.limit),a.setQueryParam("iterator",null==i?void 0:i.iterator),a.send(this.requestCtx,s.PollingEndpointOutSerializer._fromJsonObject)}consumerSeek(e,t,r,s,n){let d=new o.SvixRequest(o.HttpMethod.POST,"/api/v1/app/{app_id}/poller/{sink_id}/consumer/{consumer_id}/seek");return d.setPathParam("app_id",e),d.setPathParam("sink_id",t),d.setPathParam("consumer_id",r),d.setHeaderParam("idempotency-key",null==n?void 0:n.idempotencyKey),d.setBody(i.PollingEndpointConsumerSeekInSerializer._toJsonObject(s)),d.send(this.requestCtx,a.PollingEndpointConsumerSeekOutSerializer._fromJsonObject)}}t.MessagePoller=n},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79819:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.OperationalWebhookEndpointSecretInSerializer=void 0,t.OperationalWebhookEndpointSecretInSerializer={_fromJsonObject:e=>({key:e.key}),_toJsonObject:e=>({key:e.key})}},80320:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.RecoverOutSerializer=void 0;let i=r(92091),a=r(14593);t.RecoverOutSerializer={_fromJsonObject:e=>({id:e.id,status:i.BackgroundTaskStatusSerializer._fromJsonObject(e.status),task:a.BackgroundTaskTypeSerializer._fromJsonObject(e.task)}),_toJsonObject:e=>({id:e.id,status:i.BackgroundTaskStatusSerializer._toJsonObject(e.status),task:a.BackgroundTaskTypeSerializer._toJsonObject(e.task)})}},80481:e=>{"use strict";e.exports=require("node:readline")},81630:e=>{"use strict";e.exports=require("http")},83043:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.OperationalWebhookEndpointInSerializer=void 0,t.OperationalWebhookEndpointInSerializer={_fromJsonObject:e=>({description:e.description,disabled:e.disabled,filterTypes:e.filterTypes,metadata:e.metadata,rateLimit:e.rateLimit,secret:e.secret,uid:e.uid,url:e.url}),_toJsonObject:e=>({description:e.description,disabled:e.disabled,filterTypes:e.filterTypes,metadata:e.metadata,rateLimit:e.rateLimit,secret:e.secret,uid:e.uid,url:e.url})}},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},85367:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.default,__next_app__:()=>u,pages:()=>p,routeModule:()=>c,tree:()=>d});var i=r(57864),a=r(94327),s=r(70814),o=r(17984),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);r.d(t,n);let d={children:["",{children:["(authenticated)",{children:["webhooks",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,74967)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\webhooks\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,16703)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,52945,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,56940))).default(e)],apple:[async e=>(await Promise.resolve().then(r.bind(r,54526))).default(e)],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,36334))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,29622)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,70814)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,52945,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,56940))).default(e)],apple:[async e=>(await Promise.resolve().then(r.bind(r,54526))).default(e)],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,36334))).default(e)],twitter:[],manifest:void 0}}]}.children,p=["C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\webhooks\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},c=new i.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(authenticated)/webhooks/page",pathname:"/webhooks",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},85436:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EndpointUpdateSerializer=void 0,t.EndpointUpdateSerializer={_fromJsonObject:e=>({channels:e.channels,description:e.description,disabled:e.disabled,filterTypes:e.filterTypes,metadata:e.metadata,rateLimit:e.rateLimit,uid:e.uid,url:e.url,version:e.version}),_toJsonObject:e=>({channels:e.channels,description:e.description,disabled:e.disabled,filterTypes:e.filterTypes,metadata:e.metadata,rateLimit:e.rateLimit,uid:e.uid,url:e.url,version:e.version})}},85710:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Integration=void 0;let i=r(38787),a=r(73001),s=r(88554),o=r(20013),n=r(38137),d=r(33850);class p{constructor(e){this.requestCtx=e}list(e,t){let r=new d.SvixRequest(d.HttpMethod.GET,"/api/v1/app/{app_id}/integration");return r.setPathParam("app_id",e),r.setQueryParam("limit",null==t?void 0:t.limit),r.setQueryParam("iterator",null==t?void 0:t.iterator),r.setQueryParam("order",null==t?void 0:t.order),r.send(this.requestCtx,n.ListResponseIntegrationOutSerializer._fromJsonObject)}create(e,t,r){let a=new d.SvixRequest(d.HttpMethod.POST,"/api/v1/app/{app_id}/integration");return a.setPathParam("app_id",e),a.setHeaderParam("idempotency-key",null==r?void 0:r.idempotencyKey),a.setBody(i.IntegrationInSerializer._toJsonObject(t)),a.send(this.requestCtx,s.IntegrationOutSerializer._fromJsonObject)}get(e,t){let r=new d.SvixRequest(d.HttpMethod.GET,"/api/v1/app/{app_id}/integration/{integ_id}");return r.setPathParam("app_id",e),r.setPathParam("integ_id",t),r.send(this.requestCtx,s.IntegrationOutSerializer._fromJsonObject)}update(e,t,r){let i=new d.SvixRequest(d.HttpMethod.PUT,"/api/v1/app/{app_id}/integration/{integ_id}");return i.setPathParam("app_id",e),i.setPathParam("integ_id",t),i.setBody(o.IntegrationUpdateSerializer._toJsonObject(r)),i.send(this.requestCtx,s.IntegrationOutSerializer._fromJsonObject)}delete(e,t){let r=new d.SvixRequest(d.HttpMethod.DELETE,"/api/v1/app/{app_id}/integration/{integ_id}");return r.setPathParam("app_id",e),r.setPathParam("integ_id",t),r.sendNoResponseBody(this.requestCtx)}getKey(e,t){let r=new d.SvixRequest(d.HttpMethod.GET,"/api/v1/app/{app_id}/integration/{integ_id}/key");return r.setPathParam("app_id",e),r.setPathParam("integ_id",t),r.send(this.requestCtx,a.IntegrationKeyOutSerializer._fromJsonObject)}rotateKey(e,t,r){let i=new d.SvixRequest(d.HttpMethod.POST,"/api/v1/app/{app_id}/integration/{integ_id}/key/rotate");return i.setPathParam("app_id",e),i.setPathParam("integ_id",t),i.setHeaderParam("idempotency-key",null==r?void 0:r.idempotencyKey),i.send(this.requestCtx,a.IntegrationKeyOutSerializer._fromJsonObject)}}t.Integration=p},86592:e=>{"use strict";e.exports=require("node:inspector")},86730:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Webhook=t.WebhookVerificationError=void 0;let i=r(65038),a=r(64478),s=r(35392);class o extends Error{constructor(e){super(e),Object.setPrototypeOf(this,o.prototype),this.name="ExtendableError",this.stack=Error(e).stack}}class n extends o{constructor(e){super(e),Object.setPrototypeOf(this,n.prototype),this.name="WebhookVerificationError"}}t.WebhookVerificationError=n;class d{constructor(e,t){if(!e)throw Error("Secret can't be empty.");if((null==t?void 0:t.format)==="raw")e instanceof Uint8Array?this.key=e:this.key=Uint8Array.from(e,e=>e.charCodeAt(0));else{if("string"!=typeof e)throw Error("Expected secret to be of type string");e.startsWith(d.prefix)&&(e=e.substring(d.prefix.length)),this.key=a.decode(e)}}verify(e,t){let r={};for(let e of Object.keys(t))r[e.toLowerCase()]=t[e];let a=r["svix-id"],s=r["svix-signature"],o=r["svix-timestamp"];if((!s||!a||!o)&&(a=r["webhook-id"],s=r["webhook-signature"],o=r["webhook-timestamp"],!s||!a||!o))throw new n("Missing required headers");let d=this.verifyTimestamp(o),p=this.sign(a,d,e).split(",")[1],u=s.split(" "),c=new globalThis.TextEncoder;for(let t of u){let[r,a]=t.split(",");if("v1"===r&&(0,i.timingSafeEqual)(c.encode(a),c.encode(p)))return JSON.parse(e.toString())}throw new n("No matching signature found")}sign(e,t,r){if("string"==typeof r);else if("Buffer"===r.constructor.name)r=r.toString();else throw Error("Expected payload to be of type string or Buffer. Please refer to https://docs.svix.com/receiving/verifying-payloads/how for more information.");let i=new TextEncoder,o=Math.floor(t.getTime()/1e3),n=i.encode(`${e}.${o}.${r}`),d=a.encode(s.hmac(this.key,n));return`v1,${d}`}verifyTimestamp(e){let t=Math.floor(Date.now()/1e3),r=parseInt(e,10);if(isNaN(r))throw new n("Invalid Signature Headers");if(t-r>300)throw new n("Message timestamp too old");if(r>t+300)throw new n("Message timestamp too new");return new Date(1e3*r)}}t.Webhook=d,d.prefix="whsec_"},87063:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ListResponseEventTypeOutSerializer=void 0;let i=r(78480);t.ListResponseEventTypeOutSerializer={_fromJsonObject:e=>({data:e.data.map(e=>i.EventTypeOutSerializer._fromJsonObject(e)),done:e.done,iterator:e.iterator,prevIterator:e.prevIterator}),_toJsonObject:e=>({data:e.data.map(e=>i.EventTypeOutSerializer._toJsonObject(e)),done:e.done,iterator:e.iterator,prevIterator:e.prevIterator})}},87818:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PollingEndpointOutSerializer=void 0;let i=r(24993);t.PollingEndpointOutSerializer={_fromJsonObject:e=>({data:e.data.map(e=>i.PollingEndpointMessageOutSerializer._fromJsonObject(e)),done:e.done,iterator:e.iterator}),_toJsonObject:e=>({data:e.data.map(e=>i.PollingEndpointMessageOutSerializer._toJsonObject(e)),done:e.done,iterator:e.iterator})}},88502:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ApiTokenInSerializer=void 0,t.ApiTokenInSerializer={_fromJsonObject:e=>({name:e.name,scopes:e.scopes}),_toJsonObject:e=>({name:e.name,scopes:e.scopes})}},88554:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.IntegrationOutSerializer=void 0,t.IntegrationOutSerializer={_fromJsonObject:e=>({createdAt:new Date(e.createdAt),featureFlags:e.featureFlags,id:e.id,name:e.name,updatedAt:new Date(e.updatedAt)}),_toJsonObject:e=>({createdAt:e.createdAt,featureFlags:e.featureFlags,id:e.id,name:e.name,updatedAt:e.updatedAt})}},89009:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.IngestEndpointSecretOutSerializer=void 0,t.IngestEndpointSecretOutSerializer={_fromJsonObject:e=>({key:e.key}),_toJsonObject:e=>({key:e.key})}},89259:()=>{},89572:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EventTypeImportOpenApiInSerializer=void 0,t.EventTypeImportOpenApiInSerializer={_fromJsonObject:e=>({dryRun:e.dryRun,replaceAll:e.replaceAll,spec:e.spec,specRaw:e.specRaw}),_toJsonObject:e=>({dryRun:e.dryRun,replaceAll:e.replaceAll,spec:e.spec,specRaw:e.specRaw})}},89877:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AppPortalAccessOutSerializer=void 0,t.AppPortalAccessOutSerializer={_fromJsonObject:e=>({token:e.token,url:e.url}),_toJsonObject:e=>({token:e.token,url:e.url})}},90493:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ApiTokenExpireInSerializer=void 0,t.ApiTokenExpireInSerializer={_fromJsonObject:e=>({expiry:e.expiry}),_toJsonObject:e=>({expiry:e.expiry})}},90521:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ListResponseMessageEndpointOutSerializer=void 0;let i=r(35310);t.ListResponseMessageEndpointOutSerializer={_fromJsonObject:e=>({data:e.data.map(e=>i.MessageEndpointOutSerializer._fromJsonObject(e)),done:e.done,iterator:e.iterator,prevIterator:e.prevIterator}),_toJsonObject:e=>({data:e.data.map(e=>i.MessageEndpointOutSerializer._toJsonObject(e)),done:e.done,iterator:e.iterator,prevIterator:e.prevIterator})}},90730:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MessageInSerializer=void 0;let i=r(22235);t.MessageInSerializer={_fromJsonObject:e=>({application:e.application?i.ApplicationInSerializer._fromJsonObject(e.application):void 0,channels:e.channels,eventId:e.eventId,eventType:e.eventType,payload:e.payload,payloadRetentionHours:e.payloadRetentionHours,payloadRetentionPeriod:e.payloadRetentionPeriod,tags:e.tags,transformationsParams:e.transformationsParams}),_toJsonObject:e=>({application:e.application?i.ApplicationInSerializer._toJsonObject(e.application):void 0,channels:e.channels,eventId:e.eventId,eventType:e.eventType,payload:e.payload,payloadRetentionHours:e.payloadRetentionHours,payloadRetentionPeriod:e.payloadRetentionPeriod,tags:e.tags,transformationsParams:e.transformationsParams})}},91645:e=>{"use strict";e.exports=require("net")},92091:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.BackgroundTaskStatusSerializer=t.BackgroundTaskStatus=void 0,function(e){e.Running="running",e.Finished="finished",e.Failed="failed"}(t.BackgroundTaskStatus||(t.BackgroundTaskStatus={})),t.BackgroundTaskStatusSerializer={_fromJsonObject:e=>e,_toJsonObject:e=>e}},92352:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.HTTPValidationError=t.ValidationError=t.HttpErrorOut=void 0;class r{static getAttributeTypeMap(){return r.attributeTypeMap}}t.HttpErrorOut=r,r.discriminator=void 0,r.mapping=void 0,r.attributeTypeMap=[{name:"code",baseName:"code",type:"string",format:""},{name:"detail",baseName:"detail",type:"string",format:""}];class i{static getAttributeTypeMap(){return i.attributeTypeMap}}t.ValidationError=i,i.discriminator=void 0,i.mapping=void 0,i.attributeTypeMap=[{name:"loc",baseName:"loc",type:"Array<string>",format:""},{name:"msg",baseName:"msg",type:"string",format:""},{name:"type",baseName:"type",type:"string",format:""}];class a{static getAttributeTypeMap(){return a.attributeTypeMap}}t.HTTPValidationError=a,a.discriminator=void 0,a.mapping=void 0,a.attributeTypeMap=[{name:"detail",baseName:"detail",type:"Array<ValidationError>",format:""}]},92764:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ApplicationPatchSerializer=void 0,t.ApplicationPatchSerializer={_fromJsonObject:e=>({metadata:e.metadata,name:e.name,rateLimit:e.rateLimit,uid:e.uid}),_toJsonObject:e=>({metadata:e.metadata,name:e.name,rateLimit:e.rateLimit,uid:e.uid})}},94175:e=>{"use strict";e.exports=require("stream/web")},94735:e=>{"use strict";e.exports=require("events")},96762:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ShopifyConfigSerializer=void 0,t.ShopifyConfigSerializer={_fromJsonObject:e=>({secret:e.secret}),_toJsonObject:e=>({secret:e.secret})}},98086:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MessageAttemptOutSerializer=void 0;let i=r(60854),a=r(2289),s=r(2831);t.MessageAttemptOutSerializer={_fromJsonObject:e=>({endpointId:e.endpointId,id:e.id,msg:e.msg?a.MessageOutSerializer._fromJsonObject(e.msg):void 0,msgId:e.msgId,response:e.response,responseDurationMs:e.responseDurationMs,responseStatusCode:e.responseStatusCode,status:s.MessageStatusSerializer._fromJsonObject(e.status),timestamp:new Date(e.timestamp),triggerType:i.MessageAttemptTriggerTypeSerializer._fromJsonObject(e.triggerType),url:e.url}),_toJsonObject:e=>({endpointId:e.endpointId,id:e.id,msg:e.msg?a.MessageOutSerializer._toJsonObject(e.msg):void 0,msgId:e.msgId,response:e.response,responseDurationMs:e.responseDurationMs,responseStatusCode:e.responseStatusCode,status:s.MessageStatusSerializer._toJsonObject(e.status),timestamp:e.timestamp,triggerType:i.MessageAttemptTriggerTypeSerializer._toJsonObject(e.triggerType),url:e.url})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[5319,7911,6239,903,7838,3319,277,2644,7914,3051,4841,8004,7539,1826,3781,6648],()=>r(85367));module.exports=i})();
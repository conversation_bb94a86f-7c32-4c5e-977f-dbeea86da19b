(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2689],{29011:(e,t,s)=>{"use strict";s.d(t,{E:()=>d});var a=s(6024);s(50628);var r=s(89840),i=s(81197),n=s(31918);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:t,variant:s,asChild:i=!1,...d}=e,c=i?r.DX:"span";return(0,a.jsx)(c,{"data-slot":"badge",className:(0,n.cn)(l({variant:s}),t),...d})}},31918:(e,t,s)=>{"use strict";s.d(t,{cn:()=>i}),s(63410);var a=s(49973);s(13957);var r=s(22928);let i=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}},66592:(e,t,s)=>{Promise.resolve().then(s.bind(s,73551))},69680:(e,t,s)=>{"use strict";s.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>i,aR:()=>n});var a=s(6024);s(50628);var r=s(31918);function i(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function n(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function l(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...s})}function d(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...s})}function c(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...s})}},70234:(e,t,s)=>{"use strict";s.d(t,{$:()=>d});var a=s(6024);s(50628);var r=s(89840),i=s(81197),n=s(31918);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:s,size:i,asChild:d=!1,...c}=e,o=d?r.DX:"button";return(0,a.jsx)(o,{"data-slot":"button",className:(0,n.cn)(l({variant:s,size:i,className:t})),...c})}},73551:(e,t,s)=>{"use strict";s.d(t,{UsageAnalytics:()=>w});var a=s(6024),r=s(50628),i=s(70234),n=s(69680),l=s(29011),d=s(55844),c=s(31918);function o(e){let{className:t,value:s,...r}=e;return(0,a.jsx)(d.bL,{"data-slot":"progress",className:(0,c.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",t),...r,children:(0,a.jsx)(d.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:"translateX(-".concat(100-(s||0),"%)")}})})}var u=s(77229),x=s(10968),m=s(60326),h=s(6092),f=s(74144),g=s(84665),v=s(44e3),p=s(25866),b=s(35685),j=s.n(b);function N(e){let{data:t}=e,s=(0,r.useMemo)(()=>{let e=new Date,s=new Date(e);s.setDate(e.getDate()-29);let a=[];for(let e=0;e<30;e++){let r=new Date(s);r.setDate(s.getDate()+e);let i=t.find(e=>new Date(e.date).toDateString()===r.toDateString());a.push({date:r.toLocaleDateString("en-US",{month:"short",day:"numeric"}),cubentUnits:(null==i?void 0:i.cubentUnitsUsed)||0,requests:(null==i?void 0:i.requestsMade)||0})}return a},[t]),i=Math.max(...s.map(e=>e.cubentUnits),1),n=Math.max(...s.map(e=>e.requests),1);return(0,a.jsxs)("div",{className:"h-80 w-full",children:[(0,a.jsx)("div",{className:"flex items-end justify-between h-full space-x-1 px-4",children:s.map((e,t)=>(0,a.jsxs)("div",{className:"flex flex-col items-center flex-1 h-full max-w-8",children:[(0,a.jsxs)("div",{className:"flex-1 flex flex-col justify-end w-full space-y-1",children:[(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)("div",{className:"bg-gradient-to-t from-blue-500 to-blue-400 rounded-t-sm transition-all hover:from-blue-600 hover:to-blue-500 shadow-sm",style:{height:"".concat(i>0?Math.max(e.cubentUnits/i*180,3*(e.cubentUnits>0)):0,"px"),width:"100%"}}),(0,a.jsxs)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10 shadow-lg",children:[(0,a.jsxs)("div",{className:"font-medium",children:[e.cubentUnits.toFixed(2)," units"]}),(0,a.jsxs)("div",{className:"text-gray-300",children:[e.requests," requests"]}),(0,a.jsx)("div",{className:"text-gray-300",children:e.date})]})]}),(0,a.jsx)("div",{className:"relative group",children:(0,a.jsx)("div",{className:"bg-gradient-to-t from-green-500 to-green-400 rounded-t-sm transition-all hover:from-green-600 hover:to-green-500 shadow-sm",style:{height:"".concat(n>0?Math.max(e.requests/n*60,2*(e.requests>0)):0,"px"),width:"100%"}})})]}),t%5==0&&(0,a.jsx)("div",{className:"text-xs text-muted-foreground mt-3 transform -rotate-45 origin-left whitespace-nowrap",children:e.date})]},t))}),(0,a.jsxs)("div",{className:"flex justify-center space-x-8 mt-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-4 h-4 bg-gradient-to-t from-blue-500 to-blue-400 rounded shadow-sm"}),(0,a.jsx)("span",{className:"text-sm font-medium text-muted-foreground",children:"Cubent Units"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-4 h-4 bg-gradient-to-t from-green-500 to-green-400 rounded shadow-sm"}),(0,a.jsx)("span",{className:"text-sm font-medium text-muted-foreground",children:"Messages"})]})]}),(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Total: ",s.reduce((e,t)=>e+t.cubentUnits,0).toFixed(2)," units, "," ",s.reduce((e,t)=>e+t.requests,0)," messages"]})})]})}function w(e){let{initialData:t}=e,[s,d]=(0,r.useState)(t),[c,b]=(0,r.useState)(!1),[w,y]=(0,r.useState)(new Date),k=async()=>{b(!0);try{let e=await fetch("/api/extension/usage/stats",{method:"GET",headers:{"Content-Type":"application/json"}});if(e.ok){let t=await e.json();t.success&&(d(e=>({...e,totalCubentUnits:t.totalCubentUnits||0,totalMessages:t.totalMessages||0,userLimit:t.userLimit||50,subscriptionTier:t.subscriptionTier||"free_trial"})),y(new Date))}}catch(e){console.error("Failed to refresh usage data:",e)}finally{b(!1)}};(0,r.useEffect)(()=>{let e=setInterval(k,3e4);return()=>clearInterval(e)},[]);let U=s.totalCubentUnits/s.userLimit*100,D=U>80,A=U>100,C=(e=>{switch(e){case"pro":return{name:"Pro",icon:x.A,color:"text-yellow-600"};case"premium":return{name:"Premium",icon:m.A,color:"text-purple-600"};default:return{name:"Free Trial",icon:h.A,color:"text-blue-600"}}})(s.subscriptionTier),T=C.icon;return(0,a.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)(i.$,{variant:"ghost",size:"sm",asChild:!0,children:(0,a.jsxs)(j(),{href:"/profile",children:[(0,a.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Back to Profile"]})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Cubent Units Usage"}),s.isDemo&&(0,a.jsx)(l.E,{variant:"outline",className:"text-xs",children:"Demo Data"})]}),(0,a.jsx)("p",{className:"text-muted-foreground",children:s.isDemo?"Sample data shown - start using the extension to see real usage statistics.":"View your VS Code extension usage statistics and analytics."})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Last updated"}),(0,a.jsx)("p",{className:"text-sm font-medium",children:w.toLocaleTimeString()})]}),(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:k,disabled:c,children:[(0,a.jsx)(g.A,{className:"h-4 w-4 mr-2 ".concat(c?"animate-spin":"")}),"Refresh"]})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"Account Information"}),(0,a.jsx)(n.BT,{children:"Your profile and subscription details"})]}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)(u.Avatar,{className:"h-16 w-16",children:[(0,a.jsx)(u.AvatarImage,{src:s.user.picture,alt:s.user.name}),(0,a.jsx)(u.AvatarFallback,{children:s.user.name.charAt(0).toUpperCase()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold",children:s.user.name}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:s.user.email})]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:"Subscription:"}),(0,a.jsxs)(l.E,{variant:"secondary",className:"flex items-center gap-2",children:[(0,a.jsx)(T,{className:"h-4 w-4 ".concat(C.color)}),C.name]})]})]})]}),(0,a.jsxs)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:[(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ZB,{className:"text-sm font-medium",children:"Cubent Units"}),(0,a.jsx)(h.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(n.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:s.totalCubentUnits.toFixed(2)}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:[s.userLimit-s.totalCubentUnits," remaining of ",s.userLimit]}),(0,a.jsx)(o,{value:Math.min(U,100),className:"mt-3"}),(0,a.jsxs)("div",{className:"flex justify-between text-xs text-muted-foreground mt-2",children:[(0,a.jsxs)("span",{children:[U.toFixed(1),"% used"]}),(0,a.jsx)("span",{className:A?"text-red-600":D?"text-yellow-600":"text-green-600",children:A?"Over limit":D?"Near limit":"Within limit"})]})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ZB,{className:"text-sm font-medium",children:"Total Messages"}),(0,a.jsx)(v.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(n.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:s.totalMessages.toLocaleString()}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Total requests made"})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ZB,{className:"text-sm font-medium",children:"Efficiency"}),(0,a.jsx)(p.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(n.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:s.totalMessages>0?(s.totalCubentUnits/s.totalMessages).toFixed(2):"0.00"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Units per message"})]})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"Usage Over Time"}),(0,a.jsx)(n.BT,{children:"Daily Cubent Units usage for the last 30 days"})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)(N,{data:s.chartData})})]}),(D||A)&&"free_trial"===s.subscriptionTier&&(0,a.jsx)(n.Zp,{className:"border-yellow-200 dark:border-yellow-800 bg-yellow-50 dark:bg-yellow-950/20",children:(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(x.A,{className:"h-5 w-5 text-yellow-600 dark:text-yellow-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-yellow-800 dark:text-yellow-200",children:A?"Usage Limit Exceeded":"Approaching Usage Limit"}),(0,a.jsx)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300",children:"Upgrade to Pro for unlimited Cubent Units and advanced features."})]})]}),(0,a.jsx)(i.$,{className:"bg-yellow-600 hover:bg-yellow-700 text-white",children:"Upgrade Now"})]})})})]})}},77229:(e,t,s)=>{"use strict";s.d(t,{Avatar:()=>n,AvatarFallback:()=>d,AvatarImage:()=>l});var a=s(6024);s(50628);var r=s(17087),i=s(31918);function n(e){let{className:t,...s}=e;return(0,a.jsx)(r.bL,{"data-slot":"avatar",className:(0,i.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...s})}function l(e){let{className:t,...s}=e;return(0,a.jsx)(r._V,{"data-slot":"avatar-image",className:(0,i.cn)("aspect-square size-full",t),...s})}function d(e){let{className:t,...s}=e;return(0,a.jsx)(r.H4,{"data-slot":"avatar-fallback",className:(0,i.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...s})}}},e=>{var t=t=>e(e.s=t);e.O(0,[449,9222,7651,6580,2913,4499,7358],()=>t(66592)),_N_E=e.O()}]);
(()=>{var e={};e.id=4908,e.ids=[4908],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},8086:e=>{"use strict";e.exports=require("module")},9047:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.default,__next_app__:()=>d,pages:()=>c,routeModule:()=>p,tree:()=>a});var s=r(57864),i=r(94327),n=r(70814),o=r(17984),u={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>o[e]);r.d(t,u);let a={children:["",{children:["(authenticated)",{children:["auth-success",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,95625)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\auth-success\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,16703)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,52945,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,56940))).default(e)],apple:[async e=>(await Promise.resolve().then(r.bind(r,54526))).default(e)],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,36334))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,29622)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,70814)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,52945,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,56940))).default(e)],apple:[async e=>(await Promise.resolve().then(r.bind(r,54526))).default(e)],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,36334))).default(e)],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\auth-success\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(authenticated)/auth-success/page",pathname:"/auth-success",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:a}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},13440:e=>{"use strict";e.exports=require("util/types")},13672:(e,t,r)=>{Promise.resolve().then(r.bind(r,60763))},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},23167:(e,t,r)=>{"use strict";r.d(t,{H:()=>n});var s=r(71166),i=r(25);let n=()=>(0,s.w)({server:{DATABASE_URL:i.z.string().url()},runtimeEnv:{DATABASE_URL:process.env.DATABASE_URL}})},26824:(e,t,r)=>{Promise.resolve().then(r.bind(r,95625))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},54287:e=>{"use strict";e.exports=require("console")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},59986:(e,t,r)=>{"use strict";function s(e,t){if(e instanceof Promise)throw Error(t)}function i(e){let t=e.runtimeEnvStrict??e.runtimeEnv??process.env;if(e.emptyStringAsUndefined)for(let[e,r]of Object.entries(t))""===r&&delete t[e];if(e.skipValidation)return t;let r="object"==typeof e.client?e.client:{},i="object"==typeof e.server?e.server:{},n="object"==typeof e.shared?e.shared:{},o=e.isServer??("undefined"==typeof window||"Deno"in window),u=o?{...i,...n,...r}:{...r,...n},a=e.createFinalSchema?.(u,o)["~standard"].validate(t)??function(e,t){let r={},i=[];for(let n in e){let o=e[n]["~standard"].validate(t[n]);if(s(o,`Validation must be synchronous, but ${n} returned a Promise.`),o.issues){i.push(...o.issues.map(e=>({...e,path:[n,...e.path??[]]})));continue}r[n]=o.value}return i.length?{issues:i}:{value:r}}(u,t);s(a,"Validation must be synchronous");let c=e.onValidationError??(e=>{throw console.error("❌ Invalid environment variables:",e),Error("Invalid environment variables")}),d=e.onInvalidAccess??(()=>{throw Error("❌ Attempted to access a server-side environment variable on the client")});if(a.issues)return c(a.issues);let p=t=>!e.clientPrefix||!t.startsWith(e.clientPrefix)&&!(t in n),l=e=>o||!p(e),x=e=>"__esModule"===e||"$$typeof"===e;return new Proxy(Object.assign((e.extends??[]).reduce((e,t)=>Object.assign(e,t),{}),a.value),{get(e,t){if("string"==typeof t&&!x(t))return l(t)?Reflect.get(e,t):d(t)}})}r.d(t,{w:()=>i})},59988:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"7fa248ee4cee001992d543e3927d536ddea63c121c":()=>s.ai,"7fd10f19b29f8e8b2951e0bb60d9466e540ba24937":()=>s.ot,"7fe80fb1c9bdcbbae5a7da0586440a249dd4fb207a":()=>i.y,"7ffdf714159b7e9cad55b4d3d168d12a9fa0cc1b9f":()=>s.at});var s=r(54841),i=r(44089)},60763:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(99730);r(57752);var i=r(53172),n=r(17399);function o(){return(0,i.useSearchParams)(),(0,s.jsx)("div",{className:"flex min-h-screen items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(n.A,{className:"mx-auto h-8 w-8 animate-spin"}),(0,s.jsx)("p",{className:"mt-4 text-sm text-muted-foreground",children:"Completing authentication..."})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71166:(e,t,r)=>{"use strict";r.d(t,{w:()=>i});var s=r(59986);function i(e){let t="object"==typeof e.client?e.client:{},r="object"==typeof e.server?e.server:{},i=e.shared,n=e.runtimeEnv?e.runtimeEnv:{...process.env,...e.experimental__runtimeEnv};return(0,s.w)({...e,shared:i,client:t,server:r,clientPrefix:"NEXT_PUBLIC_",runtimeEnv:n})}},73024:e=>{"use strict";e.exports=require("node:fs")},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},91645:e=>{"use strict";e.exports=require("net")},94175:e=>{"use strict";e.exports=require("stream/web")},94735:e=>{"use strict";e.exports=require("events")},95625:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(6340).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\2 FOLDERS FOR CUBENT\\\\Cubentweb\\\\cubent\\\\apps\\\\app\\\\app\\\\(authenticated)\\\\auth-success\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\auth-success\\page.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5319,7911,6239,903,7838,3319,277,2644,7914,3051,4841,8004,3781,6648],()=>r(9047));module.exports=s})();
// Usage tracking utilities for the webview
// This mirrors the functionality from the shared usage-tracking.ts but adapted for the webview context

import { vscode } from "./vscode"
import { useAuthStore } from "../stores/authStore"

// Cubent Units mapping based on https://cubentdev.mintlify.app/models-and-pricing
const CUBENT_UNITS_MAPPING: Record<string, number> = {
	// Anthropic Claude Models
	"claude-3-7-sonnet-20250219": 1.1,
	"claude-3-7-sonnet-thinking-20250219": 1.35,
	"claude-3-5-sonnet-20241022": 0.95,
	"claude-3-5-haiku-20241022": 0.55,
	"claude-3-haiku-20240307": 0.45,

	// OpenAI Models
	"gpt-4o": 1.1,
	"gpt-4.5-preview": 1.2,
	"gpt-4o-mini": 0.65,
	"o3-mini": 1.0,
	"o3-mini-high": 1.1,
	"o3-mini-low": 0.75,

	// DeepSeek Models
	"deepseek-chat": 0.35,
	"deepseek-reasoner": 0.7,

	// Google Gemini Models
	"gemini-2.5-flash": 0.3,
	"gemini-2.5-flash-preview-05-20": 0.3, // Preview version of gemini-2.5-flash
	"gemini-2.5-flash-thinking": 0.4,
	"gemini-2.5-pro": 0.85,
	"gemini-2.0-flash": 0.45,
	"gemini-2.0-pro": 0.70,
	"gemini-1.5-flash": 0.40,
	"gemini-1.5-pro": 0.65,

	// xAI Grok Models
	"grok-3": 1.1,
	"grok-3-mini": 0.30,
	"grok-2-vision": 0.70,
}

// Usage tracking data structure
export interface UsageEntry {
	timestamp: number
	modelId: string
	cubentUnits: number
	messageCount: number
	provider: string
	configName: string
}

export interface UsageStats {
	totalCubentUnits: number
	totalMessages: number
	entries: UsageEntry[]
	lastUpdated: number
}

// Local storage key for usage data (now user-specific)
const USAGE_STORAGE_KEY_PREFIX = "cubent-usage-stats"

// Get user-specific storage key
function getUserStorageKey(userId?: string): string {
	if (!userId) {
		return `${USAGE_STORAGE_KEY_PREFIX}-anonymous`
	}
	return `${USAGE_STORAGE_KEY_PREFIX}-${userId}`
}

// Get current user ID from auth state
function getCurrentUserId(): string | null {
	try {
		// Get current user info from auth store
		// We'll use email as the unique identifier since it's available and unique
		const authState = useAuthStore.getState()
		console.log("getCurrentUserId: Full authState:", {
			isAuthenticated: authState.isAuthenticated,
			hasActiveSession: authState.hasActiveSession,
			userInfo: authState.userInfo,
			userEmail: authState.userInfo?.email
		})

		if (authState.isAuthenticated && authState.hasActiveSession && authState.userInfo?.email) {
			console.log("getCurrentUserId: returning email:", authState.userInfo.email)
			return authState.userInfo.email
		}

		// Fallback: try to get from localStorage (for cases where store isn't available)
		const storedAuthState = localStorage.getItem('auth-state')
		if (storedAuthState) {
			const parsed = JSON.parse(storedAuthState)
			const userId = parsed.userEmail || parsed.email || null
			console.log("getCurrentUserId: fallback from localStorage:", userId)
			return userId
		}
		console.log("getCurrentUserId: no user found, returning null")
		return null
	} catch (error) {
		console.warn("Could not get current user ID:", error)
		return null
	}
}

// Normalize model IDs to match our mapping (handles provider prefixes and variations)
function normalizeModelId(modelId: string): string {
	// Remove common provider prefixes
	let normalized = modelId
		.replace(/^anthropic\//, "")
		.replace(/^openai\//, "")
		.replace(/^google\//, "")
		.replace(/^deepseek\//, "")
		.replace(/^xai\//, "")
		.toLowerCase()

	// Remove version suffixes (like -001, -002, -beta, -exp, etc.)
	normalized = normalized
		.replace(/-\d{3}$/, "") // Remove -001, -002, etc.
		.replace(/-beta$/, "")
		.replace(/-exp$/, "")
		.replace(/-experimental$/, "")
		.replace(/-preview$/, "")
		.replace(/-latest$/, "")

	// Handle common model ID variations
	const mappings: Record<string, string> = {
		// Claude variations
		"claude-sonnet-4-20250514": "claude-3-7-sonnet-20250219",
		"claude-3.7-sonnet": "claude-3-7-sonnet-20250219",
		"claude-3.7-sonnet-thinking": "claude-3-7-sonnet-thinking-20250219",
		"claude-3.5-sonnet": "claude-3-5-sonnet-20241022",
		"claude-3.5-haiku": "claude-3-5-haiku-20241022",
		"claude-3-haiku": "claude-3-haiku-20240307",

		// OpenAI variations
		"gpt-4o-2024-11-20": "gpt-4o",
		"gpt-4o-mini-2024-07-18": "gpt-4o-mini",
		"o3-mini-high-reasoning": "o3-mini-high",
		"o3-mini-low-reasoning": "o3-mini-low",

		// Gemini variations
		"gemini-2.5-flash-002": "gemini-2.5-flash",
		"gemini-2.5-flash-thinking-exp": "gemini-2.5-flash-thinking",
		"gemini-2.5-pro-002": "gemini-2.5-pro",
		"gemini-2.0-flash-exp": "gemini-2.0-flash",

		// DeepSeek variations
		"deepseek-ai/deepseek-chat": "deepseek-chat",
		"deepseek-ai/deepseek-reasoner": "deepseek-reasoner",

		// Grok variations
		"grok-3-beta": "grok-3",
		"grok-3-mini-beta": "grok-3-mini",
		"grok-2-vision-beta": "grok-2-vision",
	}

	return mappings[normalized] || normalized
}

// Get Cubent units for a model (returns 0 for BYAK models or unknown models)
function getCubentUnitsForModel(modelId: string): number {
	const normalizedModelId = normalizeModelId(modelId)
	return CUBENT_UNITS_MAPPING[normalizedModelId] || 0
}

// Get current usage stats from local storage (user-specific)
export function getUsageStats(userId?: string): UsageStats {
	try {
		const currentUserId = userId || getCurrentUserId()
		const storageKey = getUserStorageKey(currentUserId || undefined)
		const stored = localStorage.getItem(storageKey)
		console.log("getUsageStats: stored data for user", currentUserId, ":", stored)
		if (stored) {
			const parsed = JSON.parse(stored) as UsageStats
			console.log("getUsageStats: parsed data:", parsed)
			// Ensure the structure is valid
			const result = {
				totalCubentUnits: parsed.totalCubentUnits || 0,
				totalMessages: parsed.totalMessages || 0,
				entries: Array.isArray(parsed.entries) ? parsed.entries : [],
				lastUpdated: parsed.lastUpdated || Date.now(),
			}
			console.log("getUsageStats: returning:", result)
			return result
		}
	} catch (error) {
		console.warn("Failed to parse usage stats from localStorage:", error)
	}

	// Return default stats if nothing stored or parsing failed
	const defaultStats = {
		totalCubentUnits: 0,
		totalMessages: 0,
		entries: [],
		lastUpdated: Date.now(),
	}
	console.log("getUsageStats: returning default stats:", defaultStats)
	return defaultStats
}

// Save usage stats to local storage (user-specific)
export function saveUsageStats(stats: UsageStats, userId?: string): void {
	try {
		const currentUserId = userId || getCurrentUserId()
		const storageKey = getUserStorageKey(currentUserId || undefined)
		localStorage.setItem(storageKey, JSON.stringify(stats))
		console.log("saveUsageStats: saved data for user", currentUserId, "to key", storageKey)
	} catch (error) {
		console.error("Failed to save usage stats to localStorage:", error)
	}
}

// Track a new message usage - USER-BASED VERSION
export function trackMessageUsage(
	modelId: string,
	provider: string,
	configName: string,
	messageCount: number = 1,
	userId?: string
): void {
	console.log("trackMessageUsage called with:", { modelId, provider, configName, messageCount, userId })

	const cubentUnits = getCubentUnitsForModel(modelId)
	console.log("Cubent units for model:", cubentUnits)

	// Get current user ID
	const currentUserId = userId || getCurrentUserId()
	console.log("Tracking usage for user:", currentUserId)

	// First, update local storage for immediate UI feedback (user-specific)
	const stats = getUsageStats(currentUserId || undefined)
	console.log("Current stats before tracking for user", currentUserId, ":", stats)

	const entry: UsageEntry = {
		timestamp: Date.now(),
		modelId,
		cubentUnits: cubentUnits * messageCount, // Will be 0 for BYAK models
		messageCount,
		provider,
		configName,
	}

	stats.entries.push(entry)
	stats.totalCubentUnits += entry.cubentUnits
	stats.totalMessages += messageCount
	stats.lastUpdated = Date.now()

	// Keep only last 1000 entries to prevent storage bloat
	if (stats.entries.length > 1000) {
		stats.entries.splice(0, stats.entries.length - 1000)
		// Recalculate totals to maintain accuracy
		stats.totalCubentUnits = stats.entries.reduce((sum, e) => sum + e.cubentUnits, 0)
		stats.totalMessages = stats.entries.reduce((sum, e) => sum + e.messageCount, 0)
	}

	saveUsageStats(stats, currentUserId || undefined)
	console.log("Local usage tracked successfully for user", currentUserId, ". New stats:", stats)

	// Now send to server for user-based tracking
	trackUserBasedUsage(modelId, provider, configName, cubentUnits, messageCount)
}

// New function to track usage on the server per user
async function trackUserBasedUsage(
	modelId: string,
	provider: string,
	configName: string,
	cubentUnits: number,
	messageCount: number
): Promise<void> {
	try {
		console.log("🚀 SENDING USER USAGE TO SERVER:", {
			modelId,
			provider,
			configName,
			cubentUnits,
			messageCount
		})

		// Send message to extension backend to track usage
		vscode.postMessage({
			type: "trackUserUsage",
			data: {
				modelId,
				provider,
				configName,
				cubentUnits,
				messageCount,
				timestamp: Date.now()
			}
		})
		console.log("✅ Message sent to extension successfully")
	} catch (error) {
		console.error("Failed to track user-based usage:", error)
	}
}

// Get usage stats for a specific time period (user-specific)
export function getUsageStatsForPeriod(days: number, userId?: string): UsageStats {
	const stats = getUsageStats(userId)
	const cutoffTime = Date.now() - (days * 24 * 60 * 60 * 1000)

	const filteredEntries = stats.entries.filter(entry => entry.timestamp >= cutoffTime)

	return {
		totalCubentUnits: filteredEntries.reduce((sum, entry) => sum + entry.cubentUnits, 0),
		totalMessages: filteredEntries.reduce((sum, entry) => sum + entry.messageCount, 0),
		entries: filteredEntries,
		lastUpdated: stats.lastUpdated,
	}
}

// Get usage breakdown by model (user-specific)
export function getUsageByModel(days?: number, userId?: string): Record<string, { cubentUnits: number; messages: number }> {
	const stats = days ? getUsageStatsForPeriod(days, userId) : getUsageStats(userId)

	return stats.entries.reduce((breakdown, entry) => {
		if (!breakdown[entry.modelId]) {
			breakdown[entry.modelId] = { cubentUnits: 0, messages: 0 }
		}
		breakdown[entry.modelId].cubentUnits += entry.cubentUnits
		breakdown[entry.modelId].messages += entry.messageCount
		return breakdown
	}, {} as Record<string, { cubentUnits: number; messages: number }>)
}

// Handle user switching - clear local data when user changes
export function handleUserSwitch(newUserId: string | null): void {
	console.log("User switched to:", newUserId)
	// Local storage is now user-specific, so no need to clear
	// Each user will have their own storage key
	// This function can be used for any cleanup if needed in the future
}

// Clear all usage data for current user
export function clearUsageStats(userId?: string): void {
	try {
		const currentUserId = userId || getCurrentUserId()
		const storageKey = getUserStorageKey(currentUserId || undefined)
		localStorage.removeItem(storageKey)
		console.log("clearUsageStats: cleared data for user", currentUserId)
	} catch (error) {
		console.error("Failed to clear usage stats:", error)
	}
}

// Export usage data as JSON for backup/analysis (user-specific)
export function exportUsageData(userId?: string): string {
	const stats = getUsageStats(userId)
	return JSON.stringify(stats, null, 2)
}

// Import usage data from JSON (user-specific)
export function importUsageData(jsonData: string, userId?: string): boolean {
	try {
		const imported = JSON.parse(jsonData) as UsageStats
		// Validate the structure
		if (typeof imported.totalCubentUnits === 'number' &&
			typeof imported.totalMessages === 'number' &&
			Array.isArray(imported.entries)) {
			saveUsageStats(imported, userId)
			return true
		}
		return false
	} catch (error) {
		console.error("Failed to import usage data:", error)
		return false
	}
}

// Debug function to check localStorage keys
export function debugStorageKeys(): void {
	console.log("=== DEBUG: All localStorage keys ===")
	for (let i = 0; i < localStorage.length; i++) {
		const key = localStorage.key(i)
		if (key && key.startsWith('cubent-usage-stats')) {
			console.log(`Key: ${key}`)
			const data = localStorage.getItem(key)
			if (data) {
				try {
					const parsed = JSON.parse(data)
					console.log(`  - Total Units: ${parsed.totalCubentUnits}`)
					console.log(`  - Total Messages: ${parsed.totalMessages}`)
					console.log(`  - Entries: ${parsed.entries?.length || 0}`)
				} catch (e) {
					console.log(`  - Invalid JSON data`)
				}
			}
		}
	}
	console.log("=== Current User ID ===")
	console.log("Current User ID:", getCurrentUserId())
	console.log("=== Auth Store State ===")
	console.log("Auth Store:", useAuthStore.getState())
}

// Clean up old mixed data and migrate to user-specific storage
export function cleanupOldData(): void {
	console.log("=== CLEANUP: Starting data cleanup ===")

	// Get current user ID
	const currentUserId = getCurrentUserId()
	console.log("CLEANUP: Current user ID:", currentUserId)

	if (!currentUserId) {
		console.log("CLEANUP: No authenticated user, skipping cleanup")
		return
	}

	// Check if user already has user-specific data
	const userStorageKey = getUserStorageKey(currentUserId)
	const existingUserData = localStorage.getItem(userStorageKey)

	if (existingUserData) {
		console.log("CLEANUP: User already has user-specific data, skipping cleanup")
		return
	}

	// Check for old anonymous data
	const anonymousKey = getUserStorageKey()
	const oldData = localStorage.getItem(anonymousKey)

	if (oldData) {
		console.log("CLEANUP: Found old anonymous data, clearing it")
		// Clear the old mixed data since it contains data from multiple users
		localStorage.removeItem(anonymousKey)
		console.log("CLEANUP: Cleared old anonymous data")
	}

	// Initialize empty user-specific data
	const emptyStats: UsageStats = {
		totalCubentUnits: 0,
		totalMessages: 0,
		entries: [],
		lastUpdated: Date.now(),
	}

	localStorage.setItem(userStorageKey, JSON.stringify(emptyStats))
	console.log("CLEANUP: Initialized empty user-specific data for", currentUserId)
}

// Initialize usage tracking on window object for use in components
export function initializeUsageTracking(): void {
	if (typeof window !== 'undefined') {
		(window as any).usageTracking = {
			getUsageStats,
			getUsageStatsForPeriod,
			getUsageByModel,
			clearUsageStats,
			exportUsageData,
			importUsageData,
			debugStorageKeys, // Add debug function
			cleanupOldData, // Add cleanup function
		}
	}
}

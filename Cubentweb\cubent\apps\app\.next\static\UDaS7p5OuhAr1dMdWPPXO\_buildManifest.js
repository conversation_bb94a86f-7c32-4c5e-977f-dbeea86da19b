self.__BUILD_MANIFEST=function(e,r,t,s,i){return{__rewrites:{afterFiles:[{has:t,source:"/ingest/static/:path*",destination:t},{has:t,source:"/ingest/:path*",destination:t},{has:t,source:"/ingest/decide",destination:t}],beforeFiles:[],fallback:[]},__routerFilterStatic:{numItems:36,errorRate:1e-4,numBits:691,numHashes:14,bitArray:[0,1,1,r,r,0,e,e,e,r,r,e,r,e,e,r,e,e,r,r,r,e,e,e,e,e,e,r,e,e,r,e,r,e,e,e,r,r,r,r,e,e,e,e,r,r,r,r,e,r,e,r,e,r,e,r,r,e,e,r,r,e,r,e,e,r,e,r,r,r,e,r,r,r,e,e,r,r,r,e,e,e,r,r,r,e,e,r,r,e,r,e,e,r,r,e,r,r,e,e,e,r,r,r,e,e,e,e,r,e,r,r,r,e,e,e,r,r,r,e,e,r,r,r,r,r,e,e,e,e,r,r,r,e,r,e,e,r,e,r,e,e,r,r,r,r,e,e,e,r,e,r,r,e,r,r,r,r,r,r,e,e,e,e,e,r,e,r,r,e,r,r,e,e,r,r,r,e,e,e,e,e,e,e,r,r,e,r,r,e,e,r,r,e,e,r,e,e,e,e,e,e,e,r,r,e,e,e,e,r,r,e,e,r,e,r,r,e,e,r,e,r,e,r,e,r,e,r,e,e,e,e,r,r,e,r,e,e,r,e,e,e,e,r,e,e,r,e,e,e,r,r,e,e,e,r,r,r,e,r,r,e,r,r,r,e,r,r,r,e,r,e,e,r,r,r,e,r,e,r,r,e,e,e,r,r,r,r,e,e,r,e,e,r,e,r,r,e,e,r,e,r,e,r,r,r,r,e,e,r,r,e,e,e,r,r,e,r,e,r,r,e,e,e,r,r,e,r,e,r,e,r,e,r,r,r,e,e,r,r,r,r,e,r,r,r,e,r,e,e,e,r,r,r,e,r,e,r,r,e,e,r,e,e,e,e,e,r,r,e,r,e,e,r,r,r,r,r,r,e,e,e,r,r,r,r,r,r,r,r,e,r,r,e,r,e,r,r,e,r,r,r,e,e,e,r,e,e,r,e,r,r,r,r,r,r,r,r,e,e,e,r,r,e,e,r,e,r,e,r,r,e,r,e,r,r,r,r,e,r,r,e,r,e,r,e,e,e,e,e,e,e,e,r,r,r,r,r,r,r,r,r,e,r,e,e,r,e,e,r,e,e,r,r,r,e,e,r,e,r,r,r,r,r,e,e,r,e,r,e,r,r,e,e,r,e,e,e,r,r,r,r,e,r,e,r,r,e,e,r,r,r,e,e,r,e,e,e,e,r,e,e,r,r,r,e,r,r,e,r,r,r,r,r,e,r,e,r,e,r,e,r,e,e,r,r,r,e,e,r,e,r,e,e,r,r,r,e,r,r,r,e,e,r,e,e,e,e,r,e,e,e,e,e,e,e,r,e,e,e,e,e,r,r,r,r,r,r,e,r,e,r,e,e,r,e,e,r,e,e,e,e,e,r,r,r,r,r,e,r,e,e,r,e,r,e,r,e,e,e,r,r,e,r,e,r,r,e,e,r,e,r,e,e,e,r,r,e,e,r,e,r,e,r,e,r,e,e,e,e,e,r,e,e,r,r,e,e,e,r,e,e,e,r,r,e,r,e,e,e,e,e,r,e,r,e,r,r,e,e,r,r,e,r,r,e,e,r,r,r,e]},__routerFilterDynamic:{numItems:2,errorRate:1e-4,numBits:39,numHashes:14,bitArray:[r,e,e,e,r,e,e,e,e,r,r,e,e,r,e,e,r,r,r,r,r,e,r,e,e,r,e,e,e,e,r,r,r,e,r,e,r,e,r]},"/_error":["static/chunks/pages/_error-bc582b1675462129.js"],sortedPages:["/_app","/_error"]}}(0,1,void 0,1e-4,14),self.__BUILD_MANIFEST_CB&&self.__BUILD_MANIFEST_CB();